<?php
namespace App\Controller;

use App\Controller\AppController;
use Cake\I18n\Time;

/**
 * Decades Controller
 *
 * @property \App\Model\Table\DecadesTable $Decades
 *
 * @method \App\Model\Entity\Decade[]|\Cake\Datasource\ResultSetInterface paginate($object = null, array $settings = [])
 */
class DecadesController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null
     */
    public function index()
    {
        $this->paginate = [
          'order'=>[
            'created_at'=>'DESC'
          ],
          'contain' => [
            'Users',
            'Imgs'=>[
              'sort'=>[
                  'created_at'=>'DESC'
                ]
              ],
            ]
        ];
        $decades = $this->paginate($this->Decades);

        $this->set(compact('decades'));
    }

    /**
     * View method
     *
     * @param string|null $id Decade id.
     * @return \Cake\Http\Response|null
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $decade = $this->Decades->get($id, [
            'contain' => ['Users', 'Imgs'],
        ]);

        $this->set('decade', $decade);
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $decade = $this->Decades->newEntity();
        if ($this->request->is('post')) {
            $decade = $this->Decades->patchEntity($decade, $this->request->getData());
            $decade->user_id = $this->Auth->user('id');
            $decade->created_at = Time::now();
            $decade->updated_at = Time::now();
            if ($this->Decades->save($decade)) {
                $this->Flash->success(__('年份创建成功。'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('年份创建失败。请联系系统管理员并稍后尝试。'));
        }
        $this->set(compact('decade'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Decade id.
     * @return \Cake\Http\Response|null Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $decade = $this->Decades->get($id, [
            'contain' => [],
        ]);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $decade = $this->Decades->patchEntity($decade, $this->request->getData());
            $decade->updated_at = Time::now();
            if ($this->Decades->save($decade)) {
                $this->Flash->success(__('年份更新成功。'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('年份更新失败。请联系系统管理员并稍后尝试。'));
        }
        $this->set(compact('decade'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Decade id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $this->Decades->Imgs->deleteAll(['decade_id'=>$id]);
        $decade = $this->Decades->get($id);
        if ($this->Decades->delete($decade)) {
            $this->Flash->success(__('年份删除成功。'));
        } else {
            $this->Flash->error(__('年份删除失败。请联系系统管理员并稍后尝试。'));
        }

        return $this->redirect(['action' => 'index']);
    }
}
