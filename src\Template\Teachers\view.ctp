<div>
  <h1><?=__('师资详情');?></h1>
  <hr>
  <?=$this->Html->link(__('更新师资信息'), ['action'=>'edit', $teacher->id], [
    'class'=>'btn btn-warning',
    'style'=>'width: 150px;'
  ]);?>
  <table class="table table-striped" style="margin-top: 30px;">
    <thead class="thead-dark">
      <tr>
        <th style="width: 20%;"><?=__('项目');?></th>
        <th style="width: 40%;"><?=__('中文');?></th>
        <th style="width: 40%;"><?=__('日文');?></th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><?=__('风采照片');?></td>
        <td colspan="2">
          <?php $image_url = ''; ?>
          <?php if (empty($teacher->url)): ?>
            <?php $image_url = 'Common/no_image.png'; ?>
          <?php else: ?>
            <?php $image_url = '../'.$teacher->url; ?>
          <?php endif; ?>
          <?=$this->Html->image($image_url, ['alt'=>__('No Image'), 'style'=>'width: 50%', 'class'=>'img-thumbnail']);?>
        </td>
      </tr>
      <tr>
        <td><?=__('师资所属');?></td>
        <td colspan="2">
          <?php if (!empty($teacher->item->name_ch)): ?>
            <span class="badge badge-dark"><?=h($teacher->item->dept?__('大学院'):__('学部'));?></span>
            <span class="badge badge-info"><?=h($teacher->item->name_ch);?></span>
          <?php else: ?>
            <span class="badge badge-danger"><?=__('暂无分类');?></span>
          <?php endif; ?>
        </td>
      </tr>
      <tr>
        <td><?=__('教师姓名');?></td>
        <td><?=h($teacher->name_ch);?></td>
        <td><?=h($teacher->name_jp);?></td>
      </tr>
      <tr>
        <td><?=__('教师介绍');?></td>
        <td><?=$this->Text->autoParagraph(h($teacher->description_ch));?></td>
        <td><?=$this->Text->autoParagraph(h($teacher->description_jp));?></td>
      </tr>
      <tr>
        <td><?=__('教师评分');?></td>
        <td colspan="2"><?=h($teacher->rank);?></td>
      </tr>
    </tbody>
  </table>
  <div style="text-align: right">
    <?=$this->Html->link(__('返回'), ['action'=>'index'], [
      'class'=>'btn btn-secondary',
      'style'=>'width: 150px;'
    ]);?>
  </div>
</div>
