<?php
/**
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link      https://cakephp.org CakePHP(tm) Project
 * @since     0.2.9
 * @license   https://opensource.org/licenses/mit-license.php MIT License
 */
namespace App\Controller;

use Cake\Controller\Controller;
use Cake\Event\Event;
use Cake\I18n\I18n;

/**
 * Application Controller
 *
 * Add your application-wide methods in the class below, your controllers
 * will inherit them.
 *
 * @link https://book.cakephp.org/3/en/controllers.html#the-app-controller
 */
 define('default_from_mail', '<EMAIL>');
 define('default_admin_mail', [
   '<EMAIL>',
   '<EMAIL>',
   '<EMAIL>'
 ]);

class AppController extends Controller
{

    /**
     * Initialization hook method.
     *
     * Use this method to add common initialization code like loading components.
     *
     * e.g. `$this->loadComponent('Security');`
     *
     * @return void
     */
    public function initialize()
    {
        parent::initialize();

        $this->loadComponent('RequestHandler', [
            'enableBeforeRedirect' => false,
        ]);
        $this->loadComponent('Flash');
        $this->loadComponent('Utility');
        $this->loadComponent('Auth', [
          'authorize'=> 'Controller',
          'authenticate' => [
            'Form' => [
                'fields' => ['username' => 'username', 'password' => 'password'],
                'userModel' => 'Users'
            ]
          ],
          'loginRedirect' => [
            'controller' => 'Home',
            'action' => 'index'
          ],
          'logoutRedirect' => [
            'controller' => 'Home',
            'action' => 'index'
          ]
        ]);

        $session = $this->request->getSession();
        if ($session->check('lang')) {
          $lang = $session->read('lang');
          I18n::setLocale($lang);
        }else {
          $sys_lang = "";
          if(strrpos(strtolower($_SERVER['HTTP_ACCEPT_LANGUAGE']), 'zh-cn') !== false) {
            $sys_lang = "ch";
          }else{
            $sys_lang = "jp";
          }
          $session->write('lang', $sys_lang);
          I18n::setLocale($sys_lang);
        }

        /*
         * Enable the following component for recommended CakePHP security settings.
         * see https://book.cakephp.org/3/en/controllers/components/security.html
         */
        //$this->loadComponent('Security');
    }

    public function isAuthorized($user)
    {
      // By default deny access.
      return true;
    }
}
