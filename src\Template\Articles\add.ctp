<div>
  <h1><?=__('创建文章');?></h1>
  <hr>
  <?=$this->Form->create($article, ['type'=>'file']);?>
  <table class="table table-striped">
    <tbody>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('文章分类');?></td>
        <td>
          <?=$this->Html->link(__('新建分类'), ['controller'=>'Items', 'action'=>'add', 'block'=>'1', 'mode'=>'add'], [
            'class'=>'btn btn-success', 'style'=>'margin-bottom: 10px;'
          ]);?>
          <?=$this->Form->select('item_id', $items);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('文章封面');?></td>
        <td>
          <?=$this->Form->input('banner', ['label'=>false, 'type'=>'file']);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('文章标题');?></td>
        <td>
          <?=$this->Form->input('title', ['label'=>false, 'placeholder'=>__('请输入文章标题')]);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('文章内容');?></td>
        <td>
          <?=$this->Form->textarea('content', ['label'=>false, 'placeholder'=>__('请输入文章内容'), 'style'=>'height: 350px;']);?>
        </td>
      </tr>
    </tbody>
  </table>
  <div style="text-align: right">
    <div style="display: inline-block">
      <?=$this->Form->submit(__('确定'), [
        'class'=>'btn btn-success',
        'style'=>'width: 150px;'
      ]);?>
    </div>
    <div style="display: inline-block">
      <?=$this->Html->link(__('返回'), ['action'=>'index'], [
        'class'=>'btn btn-secondary',
        'style'=>'width: 150px;'
      ]);?>
    </div>
  </div>
  <?=$this->Form->end();?>
</div>
<script>
$(document).ready(function(){
  CKEDITOR.replace('content', {
    filebrowserUploadUrl: '<?=$this->Url->build(['action'=>'upload']);?>',
    filebrowserUploadMethod: 'form',
  });
});
</script>
