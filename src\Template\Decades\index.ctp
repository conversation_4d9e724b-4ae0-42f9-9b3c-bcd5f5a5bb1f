<div>
  <h1><?=__('合格实绩管理一览');?></h1>
  <hr>
  <div>
    <?=$this->Html->link(__('创建年份'), ['action'=>'add'], ['class'=>'btn btn-success', 'style'=>'width: 150px;']);?>
  </div>
  <div class="mt-3">
    <?php foreach ($decades as $decade): ?>
      <div class="card mt-1 mb-1">
        <div class="card-header">
          <strong><?=h($decade->year);?><?=__('年度');?></strong>
          <?=$this->Html->link(__('更新'), ['action'=>'edit', $decade->id], ['class'=>'btn btn-info edit-btn-position']);?>
          <?=$this->Form->postLink(__('删除'), ['action'=>'delete', $decade->id], [
            'class'=>'btn btn-danger delete-btn-position',
            'confirm'=>__('确定要删除[ {0} ]吗？', $decade->year)
          ]);?>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-5">
              <div><span class="badge badge-info"><?=__('备注');?></span></div>
              <p class="card-text">
                <?php if (empty($decade->description)): ?>
                  <?=__('暂无备注');?>
                <?php else: ?>
                  <?=$this->Text->autoParagraph(h($decade->description));?>
                <?php endif; ?>
              </p>
            </div>
            <div class="col-md-7">
              <button
                id="addImgBtn"
                class="btn btn-warning"
                style="width: 150px;"
                onclick='showAddImgModal("<?=h($decade->id);?>", "<?=h($decade->year);?>")'>
                  <?=__('添加图片');?>
              </button>
              <div class="row mt-2">
                <?php foreach ($decade->imgs as $img): ?>
                  <div class="col-md-4">
                    <div class="card mt-1 mb-1">
                      <div class="card-header">
                        <?php if ($img->dept == 0): ?>
                          <span class="badge badge-pill badge-info" style="padding: 5px;"><?=__('学部合格实绩');?></span>
                        <?php endif; ?>
                        <?php if ($img->dept == 1): ?>
                          <span class="badge badge-pill badge-success" style="padding: 5px;"><?=__('大学院合格实绩');?></span>
                        <?php endif; ?>
                      </div>
                      <div class="card-body">
                        <?=$this->Html->image('../'.$img->url, ['alt'=>__('No Image'), 'style'=>'width: 100%;', 'class'=>'img-thumbnail']);?>
                      </div>
                      <div class="card-footer">
                        <?=$this->Form->postLink(__('删除'), ['controller'=>'Imgs', 'action'=>'delete', $img->id], [
                          'class'=>'btn btn-danger',
                          'style'=>'width: 100%;',
                          'confirm'=>__('确定要删除该图片吗？')
                        ]);?>
                      </div>
                    </div>
                  </div>
                <?php endforeach; ?>
              </div>
            </div>
          </div>
        </div>
      </div>
    <?php endforeach; ?>
  </div>
  <div class="paginator mt-3">
      <ul class="pagination">
          <?= $this->Paginator->first('<< ' . __('first')) ?>
          <?= $this->Paginator->prev('< ' . __('上一页')) ?>
          <?= $this->Paginator->numbers() ?>
          <?= $this->Paginator->next(__('下一页') . ' >') ?>
          <?= $this->Paginator->last(__('last') . ' >>') ?>
      </ul>
      <p><?= $this->Paginator->counter(['format' => __('Page {{page}} of {{pages}}, showing {{current}} record(s) out of {{count}} total')]) ?></p>
  </div>

  <div class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-labelledby="staticBackdropLabel" aria-hidden="true" id="addImgModal">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="addImgModalTitle"></h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <?=$this->Form->create(null, ['type'=>'file', 'url'=>['controller'=>'Imgs', 'action'=>'add']]);?>
          <?=$this->Form->hidden('selectedDecade', ['id'=>'selectedDecadeInput', 'label'=>false]);?>
          <table class="table table-striped">
            <tr>
              <td><?=$this->element('star', ['color'=>'red']);?><?=__('图片所属');?></td>
              <td>
                <?=$this->Form->select('dept', [
                  '0'=>__('学部'),
                  '1'=>__('大学院')
                ], [
                  'style'=>'margin: 5px 0px;'
                ]);?>
              </td>
            </tr>
            <tr>
              <td><?=$this->element('star', ['color'=>'red']);?><?=__('上传图片');?></td>
              <td>
                <?=$this->Form->input('url', ['type'=>'file', 'label'=>false]);?>
              </td>
            </tr>
          </table>
          <div style="text-align: right;">
            <?=$this->Form->submit(__('确定'), ['class'=>'btn btn-success']);?>
          </div>
          <?=$this->Form->end();?>
        </div>
      </div>
    </div>
  </div>
</div>
<style>
.edit-btn-position {
  position: absolute;
  top: 5px;
  right: 70px;
}
.delete-btn-position {
  position: absolute;
  top: 5px;
  right: 5px;
}
</style>
<script>
function showAddImgModal(id, year) {
  var title = year + "<?=__('年度图片追加');?>"
  $("#addImgModalTitle").html(title);
  $("#selectedDecadeInput").val(id);
  $("#addImgModal").modal("show");
}
</script>
