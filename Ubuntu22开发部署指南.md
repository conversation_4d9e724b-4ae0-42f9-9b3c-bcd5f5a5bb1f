# 心灵语教育平台 - Ubuntu 22.04 开发部署指南

## 目录
1. [环境概述](#环境概述)
2. [系统准备](#系统准备)
3. [软件安装](#软件安装)
4. [项目部署](#项目部署)
5. [配置设置](#配置设置)
6. [服务启动](#服务启动)
7. [测试验证](#测试验证)
8. [常见问题](#常见问题)
9. [维护管理](#维护管理)

## 环境概述

### 目标环境
- **操作系统**: Ubuntu 22.04 LTS (Jammy Jellyfish)
- **PHP版本**: 7.4.x (兼容项目要求的PHP >=5.6)
- **Web服务器**: Apache 2.4.x
- **数据库**: MySQL 5.7.x
- **框架**: CakePHP 3.9.x

### 版本兼容性说明
- 项目要求PHP >=5.6，我们使用PHP 7.4以获得更好的性能和安全性
- MySQL 5.7与项目数据库完全兼容
- 保持CakePHP 3.9.x版本不变，确保项目稳定性

## 系统准备

### 1. 更新系统包
```bash
# 更新包列表
sudo apt update

# 升级已安装的包
sudo apt upgrade -y

# 安装基础工具
sudo apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates
```

### 2. 设置时区
```bash
# 设置时区为亚洲/上海
sudo timedatectl set-timezone Asia/Shanghai

# 验证时区设置
timedatectl status
```

### 3. 创建项目用户（可选）
```bash
# 创建专用用户
sudo adduser xinlingyu

# 添加到sudo组
sudo usermod -aG sudo xinlingyu

# 切换到项目用户
su - xinlingyu
```

## 软件安装

### 1. 安装Apache Web服务器
```bash
# 安装Apache
sudo apt install -y apache2

# 启用必要的模块
sudo a2enmod rewrite
sudo a2enmod ssl
sudo a2enmod headers

# 启动并设置开机自启
sudo systemctl start apache2
sudo systemctl enable apache2

# 验证安装
sudo systemctl status apache2
```

### 2. 安装MySQL 5.7
```bash
# 下载MySQL APT配置包
wget https://dev.mysql.com/get/mysql-apt-config_0.8.22-1_all.deb

# 安装配置包
sudo dpkg -i mysql-apt-config_0.8.22-1_all.deb
# 在配置界面选择MySQL 5.7

# 更新包列表
sudo apt update

# 安装MySQL 5.7
sudo apt install -y mysql-server=5.7* mysql-client=5.7*

# 防止自动升级
sudo apt-mark hold mysql-server mysql-client mysql-common mysql-community-server mysql-community-client

# 安全配置
sudo mysql_secure_installation

# 启动并设置开机自启
sudo systemctl start mysql
sudo systemctl enable mysql

# 验证版本
mysql --version
```

### 3. 安装PHP 7.4及扩展
```bash
# 安装PHP 7.4和必要扩展
sudo apt install -y php7.4 php7.4-cli php7.4-common php7.4-mysql php7.4-xml php7.4-xmlrpc php7.4-curl php7.4-gd php7.4-imagick php7.4-dev php7.4-imap php7.4-mbstring php7.4-opcache php7.4-soap php7.4-zip php7.4-intl php7.4-bcmath

# 安装Apache PHP模块
sudo apt install -y libapache2-mod-php7.4

# 验证PHP安装
php -v
php -m | grep -E "(mysql|intl|mbstring|xml)"
```

### 4. 安装Composer
```bash
# 下载Composer安装脚本
curl -sS https://getcomposer.org/installer | php

# 移动到全局位置
sudo mv composer.phar /usr/local/bin/composer

# 设置执行权限
sudo chmod +x /usr/local/bin/composer

# 验证安装
composer --version
```

### 5. 安装phpMyAdmin（可选）
```bash
# 安装phpMyAdmin
sudo apt install -y phpmyadmin

# 配置Apache
sudo ln -s /usr/share/phpmyadmin /var/www/html/phpmyadmin

# 重启Apache
sudo systemctl restart apache2
```

## 项目部署

### 1. 获取项目代码
```bash
# 进入Web根目录
cd /var/www

# 克隆项目（假设从Git仓库）
sudo git clone <your-repository-url> xinlingyu
# 或者上传项目文件到此目录

# 设置目录所有权
sudo chown -R www-data:www-data /var/www/xinlingyu
sudo chmod -R 755 /var/www/xinlingyu
```

### 2. 安装项目依赖
```bash
# 进入项目目录
cd /var/www/xinlingyu

# 安装Composer依赖
sudo -u www-data composer install --no-dev --optimize-autoloader

# 设置写入权限
sudo chmod -R 777 logs/
sudo chmod -R 777 tmp/
sudo chmod -R 777 webroot/files/
```

### 3. 数据库设置
```bash
# 登录MySQL
sudo mysql -u root -p

# 创建数据库和用户
CREATE DATABASE dbforxinlingyu CHARACTER SET utf8 COLLATE utf8_general_ci;
CREATE USER 'xinlingyu_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON dbforxinlingyu.* TO 'xinlingyu_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# 导入数据库结构
mysql -u xinlingyu_user -p dbforxinlingyu < /var/www/xinlingyu/db/dbforxinlingyu.sql
```

## 配置设置

### 1. Apache虚拟主机配置
```bash
# 创建虚拟主机配置文件
sudo nano /etc/apache2/sites-available/xinlingyu.conf
```

添加以下内容：
```apache
<VirtualHost *:80>
    ServerName xinlingyu.local
    ServerAlias www.xinlingyu.local
    DocumentRoot /var/www/xinlingyu/webroot
    
    <Directory /var/www/xinlingyu/webroot>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/xinlingyu_error.log
    CustomLog ${APACHE_LOG_DIR}/xinlingyu_access.log combined
</VirtualHost>
```

```bash
# 启用站点
sudo a2ensite xinlingyu.conf

# 禁用默认站点
sudo a2dissite 000-default.conf

# 重启Apache
sudo systemctl restart apache2
```

### 2. 配置hosts文件（开发环境）
```bash
# 编辑hosts文件
sudo nano /etc/hosts

# 添加以下行
127.0.0.1    xinlingyu.local
127.0.0.1    www.xinlingyu.local
```

### 3. CakePHP应用配置
```bash
# 复制配置文件
cd /var/www/xinlingyu
sudo -u www-data cp config/app_local.example.php config/app_local.php

# 编辑配置文件
sudo -u www-data nano config/app_local.php
```

修改数据库配置：
```php
'Datasources' => [
    'default' => [
        'host' => 'localhost',
        'username' => 'xinlingyu_user',
        'password' => 'your_secure_password',
        'database' => 'dbforxinlingyu',
        'encoding' => 'utf8',
    ]
],
```

### 4. PHP配置优化
```bash
# 编辑PHP配置
sudo nano /etc/php/7.4/apache2/php.ini
```

关键配置项：
```ini
# 基础设置
memory_limit = 256M
max_execution_time = 300
max_input_time = 300
post_max_size = 64M
upload_max_filesize = 64M

# 时区设置
date.timezone = Asia/Shanghai

# 错误报告（开发环境）
display_errors = On
error_reporting = E_ALL

# 多字节字符串支持
mbstring.language = Chinese
mbstring.internal_encoding = UTF-8
```

```bash
# 重启Apache使配置生效
sudo systemctl restart apache2
```

## 服务启动

### 1. 启动所有服务
```bash
# 启动MySQL
sudo systemctl start mysql

# 启动Apache
sudo systemctl start apache2

# 检查服务状态
sudo systemctl status mysql
sudo systemctl status apache2
```

### 2. 设置开机自启
```bash
# 设置MySQL开机自启
sudo systemctl enable mysql

# 设置Apache开机自启
sudo systemctl enable apache2
```

### 3. 使用CakePHP内置服务器（开发环境）
```bash
# 进入项目目录
cd /var/www/xinlingyu

# 启动内置服务器
sudo -u www-data bin/cake server -H 0.0.0.0 -p 8765

# 访问地址：http://your-server-ip:8765
```

## 测试验证

### 1. 基础连接测试
```bash
# 测试Apache
curl -I http://xinlingyu.local

# 测试MySQL连接
mysql -u xinlingyu_user -p -e "SELECT VERSION();"

# 测试PHP
php -r "echo 'PHP is working: ' . PHP_VERSION . PHP_EOL;"
```

### 2. 应用功能测试
```bash
# 进入项目目录
cd /var/www/xinlingyu

# 运行CakePHP检查
sudo -u www-data bin/cake server --help

# 检查数据库连接
sudo -u www-data bin/cake schema_cache clear
```

### 3. 访问测试
- 浏览器访问：`http://xinlingyu.local`
- 检查首页是否正常显示
- 测试语言切换功能
- 验证文件上传功能

### 4. 运行单元测试
```bash
# 运行PHPUnit测试
cd /var/www/xinlingyu
sudo -u www-data composer test

# 运行代码规范检查
sudo -u www-data composer cs-check
```

## 常见问题

### 1. 权限问题
```bash
# 重新设置权限
sudo chown -R www-data:www-data /var/www/xinlingyu
sudo chmod -R 755 /var/www/xinlingyu
sudo chmod -R 777 /var/www/xinlingyu/logs
sudo chmod -R 777 /var/www/xinlingyu/tmp
sudo chmod -R 777 /var/www/xinlingyu/webroot/files
```

### 2. MySQL连接问题
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 重启MySQL服务
sudo systemctl restart mysql

# 检查用户权限
mysql -u root -p -e "SELECT User, Host FROM mysql.user WHERE User='xinlingyu_user';"
```

### 3. Apache重写问题
```bash
# 确保mod_rewrite已启用
sudo a2enmod rewrite

# 检查.htaccess文件
ls -la /var/www/xinlingyu/webroot/.htaccess
ls -la /var/www/xinlingyu/.htaccess

# 重启Apache
sudo systemctl restart apache2
```

### 4. PHP扩展问题
```bash
# 检查已安装的PHP扩展
php -m

# 安装缺失的扩展（如果需要）
sudo apt install -y php7.4-[extension-name]

# 重启Apache
sudo systemctl restart apache2
```

## 维护管理

### 1. 日志管理
```bash
# 查看Apache日志
sudo tail -f /var/log/apache2/xinlingyu_error.log
sudo tail -f /var/log/apache2/xinlingyu_access.log

# 查看应用日志
tail -f /var/www/xinlingyu/logs/error.log
tail -f /var/www/xinlingyu/logs/debug.log

# 清理日志（定期执行）
sudo find /var/log/apache2/ -name "*.log" -type f -mtime +30 -delete
sudo find /var/www/xinlingyu/logs/ -name "*.log" -type f -mtime +30 -delete
```

### 2. 备份策略
```bash
# 创建备份脚本
sudo nano /usr/local/bin/xinlingyu_backup.sh
```

备份脚本内容：
```bash
#!/bin/bash
BACKUP_DIR="/backup/xinlingyu"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u xinlingyu_user -p'your_secure_password' dbforxinlingyu > $BACKUP_DIR/db_$DATE.sql

# 备份文件
tar -czf $BACKUP_DIR/files_$DATE.tar.gz -C /var/www xinlingyu

# 清理30天前的备份
find $BACKUP_DIR -name "*.sql" -type f -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -type f -mtime +30 -delete
```

```bash
# 设置执行权限
sudo chmod +x /usr/local/bin/xinlingyu_backup.sh

# 添加到crontab（每日凌晨2点备份）
sudo crontab -e
# 添加：0 2 * * * /usr/local/bin/xinlingyu_backup.sh
```

### 3. 性能监控
```bash
# 安装htop监控工具
sudo apt install -y htop

# 监控Apache进程
sudo apt install -y apache2-utils
apache2ctl status

# 监控MySQL
sudo apt install -y mytop
```

### 4. 安全加固
```bash
# 配置防火墙
sudo ufw enable
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 隐藏Apache版本信息
echo "ServerTokens Prod" | sudo tee -a /etc/apache2/apache2.conf
echo "ServerSignature Off" | sudo tee -a /etc/apache2/apache2.conf

# 重启Apache
sudo systemctl restart apache2
```

---

**部署完成后访问地址**：
- 开发环境：`http://xinlingyu.local` 或 `http://your-server-ip:8765`
- 管理后台：根据应用路由配置访问

**重要提醒**：
1. 请及时修改所有默认密码
2. 生产环境请关闭PHP错误显示
3. 定期更新系统安全补丁
4. 配置SSL证书以启用HTTPS
5. 设置定期备份任务
