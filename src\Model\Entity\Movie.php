<?php
namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Movie Entity
 *
 * @property int $id
 * @property int $user_id
 * @property int $item_id
 * @property string|null $banner
 * @property string $title_ch
 * @property string $title_jp
 * @property string $description_ch
 * @property string $description_jp
 * @property string|null $url
 * @property \Cake\I18n\FrozenTime|null $created_at
 * @property \Cake\I18n\FrozenTime|null $updated_at
 *
 * @property \App\Model\Entity\User $user
 * @property \App\Model\Entity\Item $item
 */
class Movie extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array
     */
    protected $_accessible = [
        'user_id' => true,
        'item_id' => true,
        'banner' => true,
        'title_ch' => true,
        'title_jp' => true,
        'description_ch' => true,
        'description_jp' => true,
        'url' => true,
        'created_at' => true,
        'updated_at' => true,
        'user' => true,
        'item' => true,
    ];
}
