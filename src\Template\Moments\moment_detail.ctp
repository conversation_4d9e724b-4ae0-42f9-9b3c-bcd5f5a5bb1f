<?php $this->assign('title', __('最新动态'));?>
<div class="text-color">
  <h2><?=h($moment->title);?></h2>
  <hr>
  <div style="display: none;">
    <?php $moment_url = ''; ?>
    <?php if (empty($moment->banner)): ?>
      <?php $moment_url = 'Common/no_image.png'; ?>
    <?php else: ?>
      <?php $moment_url = '../'.$moment->banner; ?>
    <?php endif; ?>
    <?=$this->Html->image($moment_url, ['alt'=>__('No Image'), 'style'=>'width: 100%;']);?>
  </div>
  <div class="jumbotron mt-3">
    <?=str_replace(['<p>'],'<p class="t4">',str_replace(['<img '], '<img class="rounded img-fluid" style="width: 50%;" alt="No Image"',$moment->content));?>
  </div>
  <div class="horizon-center">
    <ul class="pagination">
      <li class="page-item <?php echo ($prev == 0)?'disabled':''; ?>">
        <?=$this->Html->link(__('Previous'), ['action'=>'momentDetail', $prev], ['class'=>'page-link']);?>
      </li>
      <li class="page-item <?php echo ($next == 0)?'disabled':''; ?>">
        <?=$this->Html->link(__('Next'), ['action'=>'momentDetail', $next], ['class'=>'page-link']);?>
      </li>
    </ul>
  </div>
  <div style="text-align: right">
    <?php
      $direction = array();
      $direction['action'] = 'index';
      if ($filterData['page'] != null) {
        $direction['page'] = $filterData['page'];
      }
      if ($filterData['dept'] != null) {
        $direction['dept'] = $filterData['dept'];
      }
      if ($filterData['category'] != null) {
        $direction['category'] = $filterData['category'];
      }
    ?>
    <?=$this->Html->link(__('返回'), $direction, [
      'class'=>'btn btn-secondary',
      'style'=>'width: 150px;'
    ]);?>
  </div>
</div>
