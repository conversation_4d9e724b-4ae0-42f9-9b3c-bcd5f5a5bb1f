<?php
namespace App\Controller;

use App\Controller\AppController;
use Cake\Event\Event;
use Cake\ORM\TableRegistry;

class MomentsController extends AppController
{
    public function beforeFilter(Event $event){
      $this->Auth->allow(['index', 'momentDetail']);
    }

    public function index()
    {
      $current_lang = $this->Utility->get_current_lang();

      $page = $this->request->getQuery('page');
      $dept = $this->request->getQuery('dept');
      $item = $this->request->getQuery('category');
      $this->Utility->updateFilter($page, $dept, $item);
      $show_categories = 0;

      $moment_conditions = array();
      if (isset($item)) {
        $moment_conditions['item_id'] = $item;
      }else {
        $moment_conditions['item_id <> '] = '0';
      }
      if (isset($dept)) {
        $show_categories = 1;
        $moment_conditions['Items.dept'] = $dept;
      }

      $categories = TableRegistry::getTableLocator()->get('Items')->find()->where(['block'=>'1', 'dept'=>$dept]);
      $moments = $this->paginate(TableRegistry::getTableLocator()->get('Articles')->find()->where($moment_conditions)->contain(['Items']));

      $this->set(compact('moments', 'show_categories', 'categories', 'current_lang'));
    }

    public function momentDetail($id)
    {
      $moment = TableRegistry::getTableLocator()->get('Articles')->get($id);

      $filter = $this->Utility->getFilterData();
      $res = $this->Utility->getMomentPrevNext($filter, $id);

      $this->set(compact('moment'));
      $this->set('filterData', $filter);
      $this->set('prev', $res['prev']);
      $this->set('next', $res['next']);
    }
}
