<?php $this->assign('title', __('关于课程'));?>
<div class="text-color">
  <h2><?=h(($current_lang == 'ch')?$lesson->title_ch:$lesson->title_jp);?></h2>
  <hr>
  <div style="display: none;">
    <?php $image_url = ''; ?>
    <?php if (empty($lesson->url)): ?>
      <?php $image_url = 'Common/no_image.png'; ?>
    <?php else: ?>
      <?php $image_url = '../'.$lesson->url; ?>
    <?php endif; ?>
    <?=$this->Html->image($image_url, ['alt'=>__('No Image'), 'style'=>'width: 100%;']);?>
  </div>
  <div class="jumbotron mt-3">
    <?=str_replace(['<p>'],'<p class="t4">',str_replace(['<img '], '<img class="rounded img-fluid" style="width: 50%;" alt="No Image"',($current_lang == 'ch')?$lesson->description_ch:$lesson->description_jp));?>
  </div>
  <div style="text-align: right">
    <?php
      $direction = array();
      $direction['action'] = 'index';
      if ($filterData['page'] != null) {
        $direction['page'] = $filterData['page'];
      }
      if ($filterData['dept'] != null) {
        $direction['dept'] = $filterData['dept'];
      }
      if ($filterData['category'] != null) {
        $direction['category'] = $filterData['category'];
      }
    ?>
    <?=$this->Html->link(__('返回'), $direction, [
      'class'=>'btn btn-secondary',
      'style'=>'width: 150px;'
    ]);?>
  </div>
</div>
