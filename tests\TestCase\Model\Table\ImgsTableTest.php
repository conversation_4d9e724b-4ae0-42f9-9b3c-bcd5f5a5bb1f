<?php
namespace App\Test\TestCase\Model\Table;

use App\Model\Table\ImgsTable;
use Cake\ORM\TableRegistry;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Table\ImgsTable Test Case
 */
class ImgsTableTest extends TestCase
{
    /**
     * Test subject
     *
     * @var \App\Model\Table\ImgsTable
     */
    public $Imgs;

    /**
     * Fixtures
     *
     * @var array
     */
    public $fixtures = [
        'app.Imgs',
        'app.Decades',
    ];

    /**
     * setUp method
     *
     * @return void
     */
    public function setUp()
    {
        parent::setUp();
        $config = TableRegistry::getTableLocator()->exists('Imgs') ? [] : ['className' => ImgsTable::class];
        $this->Imgs = TableRegistry::getTableLocator()->get('Imgs', $config);
    }

    /**
     * tearDown method
     *
     * @return void
     */
    public function tearDown()
    {
        unset($this->Imgs);

        parent::tearDown();
    }

    /**
     * Test initialize method
     *
     * @return void
     */
    public function testInitialize()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test validationDefault method
     *
     * @return void
     */
    public function testValidationDefault()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test buildRules method
     *
     * @return void
     */
    public function testBuildRules()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }
}
