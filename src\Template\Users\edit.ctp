<div>
  <h1><?=__('更新用户信息');?></h1>
  <hr>
  <?=$this->Form->create($user);?>
  <table class="table table-striped">
    <tbody>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('用户名');?></td>
        <td>
          <?=$this->Form->input('username', ['label'=>false, 'placeholder'=>__('请输入用户名')]);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('姓名');?></td>
        <td>
          <?=$this->Form->input('name', ['label'=>false, 'placeholder'=>__('请输入姓名')]);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('性别');?></td>
        <td>
          <?=$this->Form->radio('gender', [
            ['value' => '0', 'text' => __('男'), 'checked'],
            ['value' => '1', 'text' => __('女')]
          ]);?>
        </td>
      </tr>
      <tr>
        <td><?=__('联系方式');?></td>
        <td>
          <div><?=__('电话');?></div>
          <?=$this->Form->input('tel', ['label'=>false, 'placeholder'=>__('请输入联系电话')]);?>
          <div><?=$this->element('star', ['color'=>'red']);?><?=__('邮箱');?></div>
          <?=$this->Form->input('mail', ['label'=>false, 'placeholder'=>__('请输入电子邮箱')]);?>
          <div><?=__('住址');?></div>
          <?=$this->Form->input('address', ['label'=>false, 'placeholder'=>__('请输入住址')]);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('系统权限');?></td>
        <td>
          <?=$this->Form->select('role', [
            '0'=>__('管理员用户'),
            '1'=>__('一般用户')
          ]);?>
        </td>
      </tr>
    </tbody>
  </table>
  <div style="text-align: right">
    <div style="display: inline-block">
      <?=$this->Form->submit(__('更新'), [
        'class'=>'btn btn-success',
        'style'=>'width: 150px;'
      ]);?>
    </div>
    <div style="display: inline-block">
      <?=$this->Html->link(__('返回'), ['action'=>'view', $user->id], [
        'class'=>'btn btn-secondary',
        'style'=>'width: 150px;'
      ]);?>
    </div>
  </div>
  <?=$this->Form->end();?>
</div>

<style>
.form-check {
  display: inline !important;
  margin-right: 30px;
}
</style>
