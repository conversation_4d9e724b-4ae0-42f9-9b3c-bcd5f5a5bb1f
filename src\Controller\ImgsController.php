<?php
namespace App\Controller;

use App\Controller\AppController;
use Cake\I18n\Time;

/**
 * Imgs Controller
 *
 * @property \App\Model\Table\ImgsTable $Imgs
 *
 * @method \App\Model\Entity\Img[]|\Cake\Datasource\ResultSetInterface paginate($object = null, array $settings = [])
 */
class ImgsController extends AppController
{
    public function add()
    {
        $this->request->allowMethod(['post']);
        if ($this->request->is('post')) {
            $img = $this->Imgs->newEntity();
            $img = $this->Imgs->patchEntity($img, $this->request->getData());
            $img->decade_id = $this->request->getData('selectedDecade');
            $img->created_at = Time::now();
            $img->updated_at = Time::now();
            if ($this->Imgs->save($img)) {
                $this->Flash->success(__('图片追加成功。'));
            }else {
                $this->Flash->error(__('图片追加失败。请联系系统管理员并稍后尝试。'));
            }
            return $this->redirect(['controller'=>'Decades', 'action' => 'index']);
        }
    }

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $img = $this->Imgs->get($id);
        if ($this->Imgs->delete($img)) {
            $this->Flash->success(__('图片删除成功。'));
        } else {
            $this->Flash->error(__('图片删除失败。请联系系统管理员并稍后尝试。'));
        }
        return $this->redirect(['controller'=>'Decades', 'action' => 'index']);
    }
}
