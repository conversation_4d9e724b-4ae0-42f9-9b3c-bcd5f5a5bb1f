<?php
namespace App\Controller;

use App\Controller\AppController;
use Cake\I18n\Time;

/**
 * Lessons Controller
 *
 * @property \App\Model\Table\LessonsTable $Lessons
 *
 * @method \App\Model\Entity\Lesson[]|\Cake\Datasource\ResultSetInterface paginate($object = null, array $settings = [])
 */
class LessonsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null
     */
    public function index()
    {
        $this->paginate = [
            'order'=>[
              'created_at'=>'DESC'
            ],
            'contain' => ['Users','Items'],
        ];
        $lessons = $this->paginate($this->Lessons);

        if ($this->request->is('post')) {
          $conditions = array();
          $title_key = $this->request->getData('title_key');
          $conditions['item_id'] = $this->request->getData('category');
          if (!empty($title_key)) {
            $conditions['OR']['title_ch LIKE '] = '%'.$title_key.'%';
            $conditions['OR']['title_jp LIKE '] = '%'.$title_key.'%';
          }

          $lessons = $this->paginate($this->Lessons->find()->where($conditions));
        }

        $itemTable = $this->Lessons->Items;
        $items = $itemTable->find('list', [
          'keyField'=>'id',
          'valueField'=>function($itemTable){
            return $itemTable->get('label');
          }
        ])->where(['block'=>'0']);

        $this->set(compact('lessons', 'items'));
    }

    /**
     * View method
     *
     * @param string|null $id Lesson id.
     * @return \Cake\Http\Response|null
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $lesson = $this->Lessons->get($id, [
            'contain' => ['Users', 'Items'],
        ]);

        $this->set('lesson', $lesson);
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $lesson = $this->Lessons->newEntity();
        if ($this->request->is('post')) {
            $lesson = $this->Lessons->patchEntity($lesson, $this->request->getData());
            $lesson->user_id = $this->Auth->user('id');
            $lesson->created_at = Time::now();
            $lesson->updated_at = Time::now();
            if ($this->Lessons->save($lesson)) {
                $this->Flash->success(__('课程登录成功。'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('课程登录失败。请联系系统管理员并稍后尝试。'));
        }
        $itemTable = $this->Lessons->Items;
        $items = $itemTable->find('list', [
          'keyField'=>'id',
          'valueField'=>function($itemTable){
            return $itemTable->get('label');
          }
        ])->where(['block'=>'0']);
        $this->set(compact('lesson', 'items'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Lesson id.
     * @return \Cake\Http\Response|null Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $lesson = $this->Lessons->get($id, [
            'contain' => [],
        ]);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $form_data = $this->request->getData();
            if (empty($form_data['url']['size'])){
              unset($form_data['url']);
            }
            $lesson = $this->Lessons->patchEntity($lesson, $form_data);
            $lesson->updated_at = Time::now();
            if ($this->Lessons->save($lesson)) {
                $this->Flash->success(__('课程信息更新成功。'));

                return $this->redirect(['action' => 'view', $id]);
            }
            $this->Flash->error(__('课程信息更新失败。请联系系统管理员并稍后尝试。'));
        }
        $itemTable = $this->Lessons->Items;
        $items = $itemTable->find('list', [
          'keyField'=>'id',
          'valueField'=>function($itemTable){
            return $itemTable->get('label');
          }
        ])->where(['block'=>'0']);
        $this->set(compact('lesson', 'items'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Lesson id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $lesson = $this->Lessons->get($id);
        if ($this->Lessons->delete($lesson)) {
            $this->Flash->success(__('课程删除成功。'));
        } else {
            $this->Flash->error(__('课程删除失败。请联系系统管理员并稍后尝试。'));
        }

        return $this->redirect(['action' => 'index']);
    }

    public function upload(){
      $this->request->allowMethod(['post']);

      if (isset($_FILES['upload']['name'])) {
        return $this->Utility->uploadContentImage($_FILES['upload']['name']);
      }
    }
}
