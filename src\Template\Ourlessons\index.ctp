<?= $this->Html->css('Ourlessons/index.css') ?>
<?php $this->start('script'); ?>
<?= $this->Html->script('common/page.js') ?>
<?php $this->end(); ?>
<?php $this->assign('title', __('关于课程'));?>
<div class="text-color">
  <?=$this->element('filter', ['categories'=>$categories]);?>
  <hr>
  <div>
    <div class="row">
      <?php foreach ($lessons as $lesson): ?>
        <div class="col-md-4">
          <div class="card card-border-style">
            <?php $lesson_url = ''; ?>
            <?php if (empty($lesson->url)): ?>
              <?php $lesson_url = 'Common/no_image.png'; ?>
            <?php else: ?>
              <?php $lesson_url = '../'.$lesson->url; ?>
            <?php endif; ?>
            
            <div class="lesson-card-image">
              <?=$this->Html->image($lesson_url, [
                'alt'=>__('No Image'),
                'class'=>'card-image',
                'url'=>[
                  'action'=>'lessonDetail',
                  $lesson->id
                ]
              ]);?>
            </div>


            <div class="card-body news-text">
              <div><span class="badge badge-info"><?=h(($current_lang == 'ch')?$lesson->item->name_ch:$lesson->item->name_jp);?></span></div>
              <p class="card-text"><?=h(($current_lang == 'ch')?$lesson->title_ch:$lesson->title_jp);?></p>
            </div>
          </div>
        </div>
      <?php endforeach; ?>
    </div>
    <div class="paginator mt-5 horizon-center">
        <ul class="pagination">
            <?= $this->Paginator->first('<< ' . __('first')) ?>
            <?= $this->Paginator->prev('< ' . __('上一页')) ?>
            <?= $this->Paginator->numbers() ?>
            <?= $this->Paginator->next(__('下一页') . ' >') ?>
            <?= $this->Paginator->last(__('last') . ' >>') ?>
        </ul>
    </div>
  </div>
</div>

<style>
.card-body{
    overflow-y: scroll;
    overflow: -moz-scrollbars-none;/* 旧版本firefox对应 */
    -ms-overflow-style: none; /* IE 10+对应 */
    scrollbar-width: none; /* 最新版本firefox对应 */
}
.card-body::-webkit-scrollbar { 
    display: none; /* chrome和safari对应 */
    width: 0 !important
}
</style>