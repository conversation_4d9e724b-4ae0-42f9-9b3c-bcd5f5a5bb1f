<div>
  <h1><?=__('文章详情');?></h1>
  <hr>
  <?=$this->Html->link(__('更新文章信息'), ['action'=>'edit', $article->id], [
    'class'=>'btn btn-warning',
    'style'=>'width: 150px;'
  ]);?>
  <table class="table table-striped" style="margin-top: 30px;">
    <tbody>
      <tr>
        <td rowspan="3" style="width: 40%;">
          <?php $image_url = ''; ?>
          <?php if (empty($article->banner)): ?>
            <?php $image_url = 'Common/no_image.png'; ?>
          <?php else: ?>
            <?php $image_url = '../'.$article->banner; ?>
          <?php endif; ?>
          <?=$this->Html->image($image_url, ['alt'=>__('No Image'), 'style'=>'width: 50%', 'class'=>'img-thumbnail']);?>
        </td>
        <td style="width: 20%;"><?=__('分类所属');?></td>
        <td>
          <?php if (!empty($article->item->name_ch)): ?>
            <span class="badge badge-dark"><?=h($article->item->dept?__('大学院'):__('学部'));?></span>
            <span class="badge badge-info"><?=h($article->item->name_ch);?></span>
          <?php else: ?>
            <span class="badge badge-danger"><?=__('暂无分类');?></span>
          <?php endif; ?>
        </td>
      </tr>

      <tr>
        <td><?=__('文章标题');?></td>
        <td><?=h($article->title);?></td>
      </tr>
      <tr>
        <td><?=__('文章内容');?></td>
        <td>
          <?=str_replace(['<p>'],'<p class="t4">',str_replace(['<img '], '<img class="rounded img-fluid" style="width: 50%;" alt="No Image"',$article->content));?>
        </td>
      </tr>
    </tbody>
  </table>
  <div style="text-align: right">
    <?=$this->Html->link(__('返回'), ['action'=>'index'], [
      'class'=>'btn btn-secondary',
      'style'=>'width: 150px;'
    ]);?>
  </div>
</div>
