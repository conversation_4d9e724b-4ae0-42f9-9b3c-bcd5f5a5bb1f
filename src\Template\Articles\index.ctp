<div>
  <h1><?=__('文章管理一览');?></h1>
  <hr>
  <div class="row">
    <div class="col-md-2">
      <?=$this->Html->link(__('新建文章'), ['action'=>'add'], [
        'class'=>'btn btn-success',
        'style'=>'width: 150px;'
      ]);?>
    </div>
    <div class="col-md-2">
      <button class="btn btn-warning" style="width: 150px;" type="button" data-toggle="collapse" data-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
        <?=__('筛选条件');?>
      </button>
    </div>
  </div>
  <div class="filter-box">
    <div class="collapse" id="filterCollapse">
      <div class="card card-body">
        <?=$this->Form->create();?>
        <table class="table table-borderless">
          <tr>
            <td>
              <?=$this->Form->select('category_key', $items);?>
            </td>
            <td>
              <?=$this->Form->input('title_key', ['label'=>false, 'placeholder'=>__('请输入您要检索文章的标题')])?>
            </td>
          </tr>
        </table>
        <div style="text-align: right;">
          <div class="d-inline-block">
            <?=$this->Form->submit(__('检索'), ['class'=>'btn btn-dark']);?>
          </div>
          <div class="d-inline-block">
            <?=$this->Html->link(__('重置'), ['action'=>'index'], ['class'=>'btn btn-secondary']);?>
          </div>
        </div>
        <?=$this->Form->end();?>
      </div>
    </div>
  </div>

  <table class="table table-striped" style="margin-top: 30px;">
    <thead class="thead-dark">
      <tr>
        <th scope="col">#</th>
        <th scope="col" style="width: 15%;"><?=__('分类所属');?></th>
        <th scope="col" style="width: 15%;"><?=__('文章封面');?></th>
        <th scope="col" style="width: 15%;"><?=__('文章标题');?></th>
        <th scope="col" style="width: 15%;"><?=__('登录人');?></th>
        <th scope="col" style="width: 15%;"><?=__('创建日期');?></th>
        <th scope="col"><?=__('操作选项');?></th>
      </tr>
    </thead>
    <tbody>
      <?php $count_num = 1; ?>
      <?php foreach ($articles as $article): ?>
        <tr>
          <td><?=h($count_num);?></td>
          <td>
            <?php if (!empty($article->item->name_ch)): ?>
              <div><span class="badge badge-dark"><?=h($article->item->dept?__('大学院'):__('学部'));?></span></div>
              <div><span class="badge badge-info"><?=h($article->item->name_ch);?></span></div>
            <?php else: ?>
              <span class="badge badge-danger"><?=__('暂无分类');?></span>
            <?php endif; ?>
          </td>
          <td>
            <?php $image_url = ''; ?>
            <?php if (empty($article->banner)): ?>
              <?php $image_url = 'Common/no_image.png'; ?>
            <?php else: ?>
              <?php $image_url = '../'.$article->banner; ?>
            <?php endif; ?>
            <?=$this->Html->image($image_url, ['alt'=>__('No Image'), 'style'=>'width: 100%', 'class'=>'img-thumbnail']);?>
          </td>
          <td><?=h($article->title);?></td>
          <td><?=h($article->user->username);?></td>
          <td><?=h(date('Y-n-d H:i', strtotime($article->created_at)));?></td>
          <td>
            <?=$this->Html->link(__('详情'), ['action'=>'view', $article->id], [
              'class'=>'btn btn-info'
            ]);?>
            <?=$this->Form->postLink(__('删除'), ['action'=>'delete', $article->id], [
              'class'=>'btn btn-danger',
              'confirm'=>__('确定要删除[ {0} ]吗？', $article->title)
            ]);?>
          </td>
        </tr>
        <?php $count_num = $count_num + 1; ?>
      <?php endforeach; ?>
    </tbody>
  </table>

  <div class="paginator">
      <ul class="pagination">
          <?= $this->Paginator->first('<< ' . __('first')) ?>
          <?= $this->Paginator->prev('< ' . __('上一页')) ?>
          <?= $this->Paginator->numbers() ?>
          <?= $this->Paginator->next(__('下一页') . ' >') ?>
          <?= $this->Paginator->last(__('last') . ' >>') ?>
      </ul>
      <p><?= $this->Paginator->counter(['format' => __('Page {{page}} of {{pages}}, showing {{current}} record(s) out of {{count}} total')]) ?></p>
  </div>
</div>

<style>
.filter-box {
  width: 50%;
  margin-top: 10px;
}
</style>
