name-template: 'v$NEXT_PATCH_VERSION 🌈'
tag-template: 'v$NEXT_PATCH_VERSION'
prerelease: true
exclude-labels:
  - 'skip-changelog'
categories:
  - title: '🚀 Features'
    labels:
      - 'new-feature'
      - 'feature'
      - 'enhancement'
  - title: '🐛 Bug fixes'
    labels:
      - 'fix'
      - 'bugfix'
      - 'bug'
  - title: '📖 Docs'
    labels:
      - 'docs'
  - title: '📦 Dependencies'
    labels:
      - 'dependencies'
  - title: '🧰 Maintenance'
    label: 'chore'
change-template: '- #$NUMBER: $TITLE'
template: |
  ## Changes

  $CHANGES
