name: Deploy

on:
  release:
    types:
      - published

env:
  FORCE_COLOR: 2
  NODE: 14.x

jobs:
  test:
    runs-on: ubuntu-latest
    if: github.repository == 'twbs/icons'

    steps:
      - name: Clone repository
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: "${{ env.NODE }}"

      - name: Set up npm cache
        uses: actions/cache@v2
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ env.NODE }}-${{ hashFiles('package.json') }}-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-${{ env.NODE }}-${{ hashFiles('package.json') }}-${{ hashFiles('package-lock.json') }}

      - name: Install npm dependencies
        run: npm ci

      - name: Build the icons
        run: npm run icons

      - name: Build the docs
        run: npm run docs-build

      - name: Deploy
        uses: peaceiris/actions-gh-pages@v3
        with:
          allow_empty_commit: false
          personal_token: ${{ secrets.PERSONAL_TOKEN }}
          publish_branch: gh-pages
          publish_dir: ./_site/
