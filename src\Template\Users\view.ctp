<div>
  <h1><?=__('用户详情');?></h1>
  <hr>
  <?php if ($user->id != 1): ?>
    <?=$this->Html->link(__('更新用户信息'), ['action'=>'edit', $user->id], [
      'class'=>'btn btn-warning',
      'style'=>'width: 150px;'
    ]);?>
  <?php endif; ?>
  <table class="table table-striped" style="margin-top: 30px;">
    <tbody>
      <tr>
        <td style="width: 25%;"><?=__('用户名');?></td>
        <td><?=h($user->username);?></td>
      </tr>
      <tr>
        <td><?=__('姓名');?></td>
        <td><?=h($user->name);?></td>
      </tr>
      <tr>
        <td><?=__('性别');?></td>
        <td>
          <?=h($user->gender?__('女'):__('男'));?>
        </td>
      </tr>
      <tr>
        <td><?=__('联系方式');?></td>
        <td>
          <div>
            <i class="bi bi-telephone icon-style"></i><span class="badge badge-info"><?=h($user->tel);?></span>
          </div>
          <div>
            <i class="bi bi-envelope-open icon-style"></i><span class="badge badge-info"><?=h($user->mail);?></span>
          </div>
          <div>
            <i class="bi bi-geo-alt icon-style"></i><span class="badge badge-info"><?=h($user->address);?></span>
          </div>
        </td>
      </tr>
      <tr>
        <td><?=__('系统权限');?></td>
        <td>
          <?=h($user->role?__('一般用户'):__('管理员用户'));?>
        </td>
      </tr>
    </tbody>
  </table>
  <div style="text-align: right">
    <?=$this->Html->link(__('返回'), ['action'=>'index'], [
      'class'=>'btn btn-secondary',
      'style'=>'width: 150px;'
    ]);?>
  </div>
</div>

<style>
.icon-style {
  margin-right: 10px;
}
</style>
