<?php
namespace App\Controller;

use App\Controller\AppController;
use Cake\Event\Event;
use Cake\I18n\I18n;

class LangsController extends AppController
{
    public function beforeFilter(Event $event){
      $this->Auth->allow(['change']);
    }

    public function change($lang = null){
      $url = $this->referer();
      $this->request->allowMethod('post');
      $session = $this->request->getSession();
      $session->write('lang', $lang);
      I18n::setLocale($lang);
      $this->redirect($url);
    }
}
