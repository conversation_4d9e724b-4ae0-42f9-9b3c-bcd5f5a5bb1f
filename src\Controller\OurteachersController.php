<?php
namespace App\Controller;

use App\Controller\AppController;
use Cake\Event\Event;
use Cake\ORM\TableRegistry;

class OurteachersController extends AppController
{
    public function beforeFilter(Event $event){
      $this->Auth->allow(['index']);
    }

    public function index()
    {
      $current_lang = $this->Utility->get_current_lang();

      $dept = $this->request->getQuery('dept');
      $item = $this->request->getQuery('category');
      $show_categories = 0;

      $teacher_conditions = array();
      if (isset($item)) {
        $teacher_conditions['item_id'] = $item;
      }else {
        $teacher_conditions['item_id <> '] = '0';
      }
      if (isset($dept)) {
        $show_categories = 1;
        $teacher_conditions['Items.dept'] = $dept;
      }

      $categories = TableRegistry::getTableLocator()->get('Items')->find()->where(['block'=>'3', 'dept'=>$dept]);
      $teachers = $this->paginate(TableRegistry::getTableLocator()->get('Teachers')->find()->where($teacher_conditions)->contain(['Items'])->order(['rank'=>'DESC']));

      $this->set(compact('teachers', 'show_categories', 'categories', 'dept', 'current_lang'));
    }
}
