<?php
namespace App\Controller;

use App\Controller\AppController;
use Cake\I18n\Time;
use Cake\ORM\TableRegistry;

/**
 * Items Controller
 *
 * @property \App\Model\Table\ItemsTable $Items
 *
 * @method \App\Model\Entity\Item[]|\Cake\Datasource\ResultSetInterface paginate($object = null, array $settings = [])
 */
class ItemsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null
     */
    public function index()
    {
        $this->paginate = [
            'order'=>['created_at'=>'DESC'],
            'contain' => ['Users'],
        ];
        $items = $this->paginate($this->Items);

        if ($this->request->is('post')) {
          $conditions = array();
          $block_key = $this->request->getData('block_key');
          $dept_key = $this->request->getData('dept_key');
          $name_key = $this->request->getData('name_key');
          if ($block_key != 'all') {
            $conditions['block'] = $block_key;
          }
          if ($dept_key != 'all') {
            $conditions['dept'] = $dept_key;
          }
          if (!empty($name_key)) {
            $conditions['OR']['name_ch LIKE '] = '%'.$name_key.'%';
            $conditions['OR']['name_jp LIKE '] = '%'.$name_key.'%';
          }

          $items = $this->paginate($this->Items->find()->where($conditions));
        }

        $this->set(compact('items'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null Redirects on successful add, renders view otherwise.
     */
    public function add($id = null)
    {
        $block = $this->request->getQuery('block'); // from
        $mode = $this->request->getQuery('mode'); // action
        $item = $this->Items->newEntity();
        if ($this->request->is('post')) {
            $item = $this->Items->patchEntity($item, $this->request->getData());
            $item->user_id = $this->Auth->user('id');
            $item->created_at = Time::now();
            $item->updated_at = Time::now();
            if ($this->Items->save($item)) {
                $this->Flash->success(__('分类创建成功'));

                $redirect_url = array();
                if (isset($block)) {
                  if ($block == 0) {
                    $redirect_url['controller'] = 'Lessons';
                  }
                  if ($block == 1) {
                    $redirect_url['controller'] = 'Articles';
                  }
                  if ($block == 2) {
                    $redirect_url['controller'] = 'Movies';
                  }
                  if ($block == 3) {
                    $redirect_url['controller'] = 'Teachers';
                  }
                  $redirect_url['action'] = $mode;
                  if (!empty($id)) {
                    array_push($redirect_url, $id);
                  }
                }else {
                  $redirect_url['action'] = 'index';
                }

                return $this->redirect($redirect_url);
            }
            $this->Flash->error(__('分类创建失败。请联系系统管理员并稍后尝试。'));
        }
        $this->set(compact('item'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Item id.
     * @return \Cake\Http\Response|null Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $item = $this->Items->get($id, [
            'contain' => [],
        ]);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $form_data = $this->request->getData();
            if (empty($form_data['url']['size'])){
              unset($form_data['url']);
            }
            $item = $this->Items->patchEntity($item, $form_data);
            $item->updated_at = Time::now();
            if ($this->Items->save($item)) {
                $this->Flash->success(__('分类更新成功。'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('分类更新失败。请联系系统管理员并稍后尝试。'));
        }
        $this->set(compact('item'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Item id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $lessons = TableRegistry::getTableLocator()->get('Lessons')->find()->where(['item_id'=>$id]);
        $articles = TableRegistry::getTableLocator()->get('Articles')->find()->where(['item_id'=>$id]);
        $movies = TableRegistry::getTableLocator()->get('Movies')->find()->where(['item_id'=>$id]);
        foreach ($lessons as $lesson) {
          $lesson->item_id = 0;
          TableRegistry::getTableLocator()->get('Lessons')->save($lesson);
        }
        foreach ($articles as $article) {
          $article->item_id = 0;
          TableRegistry::getTableLocator()->get('Articles')->save($article);
        }
        foreach ($movies as $movie) {
          $movie->item_id = 0;
          TableRegistry::getTableLocator()->get('Movies')->save($movie);
        }
        $item = $this->Items->get($id);
        if ($this->Items->delete($item)) {
            $this->Flash->success(__('分类删除成功。'));
        } else {
            $this->Flash->error(__('分类删除失败。请联系系统管理员并稍后尝试。'));
        }

        return $this->redirect(['action' => 'index']);
    }
}
