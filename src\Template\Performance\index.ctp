<?= $this->Html->css('Performance/index.css') ?>
<?php $this->assign('title', __('合格实绩'));?>
<div class="text-color">
  <div class="jumbotron jumbotron-background">
    <div class="row">
      <div class="col-md-6">
        <?=$this->Html->image('Performance/img.png', ['alt'=>__('No Image'), 'style'=>'width: 100%']);?>
        <div class="horizon-center">
          <div>
            <?php foreach ($decades as $decade): ?>
              <div class="decades-box">
                <?=$this->Html->link($decade->year, ['action'=>'index', 'year'=>$decade->id, 'dept'=>'1'], [
                  'class'=>'decades-a-link',
                  'style'=>($this->request->query('year') == $decade->id)?'color: white;':''
                ]);?>
              </div>
            <?php endforeach; ?>
          </div>
        </div>
        <div class="title-text-box">
          <strong class="title-text"><?=__('新领域理工合格实绩');?></strong>
        </div>
      </div>
      <div class="col-md-6">
        <div class="row">
          <div class="col-md-2 dept-selector-box">
            <div class="title-university">
              <?=$this->Html->link(__('学部'), ['action'=>'index', 'year'=>$this->request->query('year'), 'dept'=>'0'], ['class'=>'dept-a-link', 'style'=>($this->request->query('dept') != null && $this->request->query('dept') == 0)?'color: white;':'']);?>
            </div>
            <div class="title-research">
              <?=$this->Html->link(__('大学院'), ['action'=>'index', 'year'=>$this->request->query('year'), 'dept'=>'1'], ['class'=>'dept-a-link', 'style'=>($this->request->query('dept') == 1)?'color: white;':'']);?>
            </div>
            
          </div>
          <div class="col-md-10">
            <?php foreach ($imgs as $img): ?>
              <?=$this->Html->image('../'.$img->url, ['alt'=>__('No Image'), 'style'=>'width: 100%;', 'class'=>'img-thumbnail']);?>
            <?php endforeach; ?>
            <div class="horizon-center horizon-right">
              <div class="paginator">
                  <ul class="pagination">
                      <?=$this->Paginator->options([
                        'url' => [
                          'year' => $this->request->query('year'),
                          'dept' => $this->request->query('dept')
                        ]
                      ]);?>
                      <?= $this->Paginator->numbers() ?>
                  </ul>
                  <p><?= $this->Paginator->counter(['format' => __('Page {{page}} of {{pages}}, showing {{current}} record(s) out of {{count}} total')]) ?></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
