<?php
namespace App\Controller;

use App\Controller\AppController;
use Cake\I18n\Time;

/**
 * Articles Controller
 *
 * @property \App\Model\Table\ArticlesTable $Articles
 *
 * @method \App\Model\Entity\Article[]|\Cake\Datasource\ResultSetInterface paginate($object = null, array $settings = [])
 */
class ArticlesController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null
     */
    public function index()
    {
        $this->paginate = [
            'order'=>[
              'created_at'=>'DESC'
            ],
            'contain' => ['Users', 'Items'],
        ];
        $articles = $this->paginate($this->Articles);

        if ($this->request->is('post')) {
          $conditions = array();
          $title_key = $this->request->getData('title_key');
          $conditions['item_id'] = $this->request->getData('category_key');
          if (!empty($title_key)) {
            $conditions['title LIKE '] = '%'.$title_key.'%';
          }

          $articles = $this->paginate($this->Articles->find()->where($conditions));
        }
        $itemTable = $this->Articles->Items;
        $items = $itemTable->find('list', [
          'keyField'=>'id',
          'valueField'=>function($itemTable){
            return $itemTable->get('label');
          }
        ])->where(['block'=>'1']);
        $this->set(compact('articles', 'items'));
    }

    /**
     * View method
     *
     * @param string|null $id Article id.
     * @return \Cake\Http\Response|null
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $article = $this->Articles->get($id, [
            'contain' => ['Users', 'Items'],
        ]);

        $this->set('article', $article);
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $article = $this->Articles->newEntity();
        if ($this->request->is('post')) {
            $article = $this->Articles->patchEntity($article, $this->request->getData());
            $article->user_id = $this->Auth->user('id');
            $article->created_at = Time::now();
            $article->updated_at = Time::now();
            if ($this->Articles->save($article)) {
                $this->Flash->success(__('创建文章成功。'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('创建文章失败。请联系系统管理员并稍后尝试。'));
        }
        $itemTable = $this->Articles->Items;
        $items = $itemTable->find('list', [
          'keyField'=>'id',
          'valueField'=>function($itemTable){
            return $itemTable->get('label');
          }
        ])->where(['block'=>'1']);
        $this->set(compact('article', 'items'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Article id.
     * @return \Cake\Http\Response|null Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $article = $this->Articles->get($id, [
            'contain' => [],
        ]);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $form_data = $this->request->getData();
            if (empty($form_data['banner']['size'])){
              unset($form_data['banner']);
            }
            $article = $this->Articles->patchEntity($article, $form_data);
            $article->updated_at = Time::now();
            if ($this->Articles->save($article)) {
                $this->Flash->success(__('文章更新成功。'));

                return $this->redirect(['action' => 'view', $id]);
            }
            $this->Flash->error(__('文章更新失败。请联系系统管理员并稍后尝试。'));
        }
        $itemTable = $this->Articles->Items;
        $items = $itemTable->find('list', [
          'keyField'=>'id',
          'valueField'=>function($itemTable){
            return $itemTable->get('label');
          }
        ])->where(['block'=>'1']);
        $this->set(compact('article', 'items'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Article id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $article = $this->Articles->get($id);
        if ($this->Articles->delete($article)) {
            $this->Flash->success(__('文章删除成功。'));
        } else {
            $this->Flash->error(__('文章删除失败。请联系系统管理员并稍后尝试。'));
        }

        return $this->redirect(['action' => 'index']);
    }

    public function upload(){
      $this->request->allowMethod(['post']);

      if (isset($_FILES['upload']['name'])) {
        return $this->Utility->uploadContentImage($_FILES['upload']['name']);
      }
    }
}
