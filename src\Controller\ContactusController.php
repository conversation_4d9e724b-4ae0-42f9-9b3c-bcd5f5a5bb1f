<?php
namespace App\Controller;

use App\Controller\AppController;
use Cake\Event\Event;
use App\Form\ContactForm;

class ContactusController extends AppController
{
    public function beforeFilter(Event $event){
      $this->Auth->allow(['index']);
    }

    public function index()
    {
      $contact = new ContactForm();

      if ($this->request->is('post')) {
        if ($res = $contact->execute($this->request->getData())) {
          $this->Utility->send_mail($res, 'contact_mail', default_from_mail, default_admin_mail, __('【咨询提醒】您有一条新的咨询'));
          $this->Flash->success('感谢您的配合。您的咨询内容已经提交，我们会尽快与您取得联系。');
          return $this->redirect(['action' => 'index']);
        }else {
          $this->Flash->error('诚挚的向您致歉。以上您提交的内容中有误，请确认后再次提交。');
        }
      }

      $this->set(compact(['contact']));
    }
}
