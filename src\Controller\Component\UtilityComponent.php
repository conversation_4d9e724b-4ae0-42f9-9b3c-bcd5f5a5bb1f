<?php
namespace App\Controller\Component;

use Cake\Controller\Component;
use Cake\Controller\ComponentRegistry;
use Cake\Mailer\Email;
use Cake\Routing\Router;
use Cake\ORM\TableRegistry;

/**
 * Utility component
 */
class UtilityComponent extends Component
{
    /**
     * Default configuration.
     *
     * @var array
     */
    protected $_defaultConfig = [];

    public function get_current_lang(){
      $session = $this->request->getSession();
      $current_lang = $session->read('lang');
      return $current_lang;
    }

    /**
     * 发送邮件.
     *
     * @data array 邮件内所包含的内容
     * @template string 所使用的邮件模板
     * @from string 发送者
     * @to string/array 接受者
     * @subject string 邮件主题
     */
    public function send_mail($data, $template, $from, $to, $subject){
      $e_mail = new Email();
      $e_mail->setTransport('xinlingyu');
      $e_mail->setViewVars(['data'=>$data]);
      $e_mail->viewBuilder()->setHelpers(['Html','Url','Text']);
      $e_mail->viewBuilder()->setTemplate($template,'default');
      $e_mail->setEmailFormat('text')
           ->setFrom($from)
           ->setTo($to)
           ->setSubject($subject)
           ->send();
    }

    public function uploadContentImage($data){
      // $showimgurl = 'http://'.$_SERVER['HTTP_HOST'].'/'.basename(dirname(APP)).'/webroot/files/article_images/';
      $showimgurl = Router::url('/', true).'webroot/files/article_images/';
      $uploaddir = '../webroot/files/article_images/';
      $file = $_FILES['upload']['tmp_name'];
      $file_name = $_FILES['upload']['name'];

      $file_name_array = explode(".", $file_name);
      $extension = end($file_name_array);
      $new_image_name = rand() . '.' . $extension;

      if (!is_dir($uploaddir)) {
        mkdir($uploaddir);
      }
      $allowed_extension = array("jpeg", "jpg", "gif", "png");

      $function_number = $_GET['CKEditorFuncNum'];
      $url = $showimgurl.$new_image_name;
      if (in_array($extension, $allowed_extension)) {
        if (move_uploaded_file($file, $uploaddir.$new_image_name)) {
          $url = $showimgurl.$new_image_name;
          $message = __('图片上传成功。');
        }else {
          $url = '';
          $message = __('图片上传失败。');
        }
      }else {
        $url = '';
        $message = __('图片格式错误。');
      }
      echo "<script type='text/javascript'>window.parent.CKEDITOR.tools.callFunction($function_number, '$url', '$message');</script>";
      return ture;
    }

    public function updateFilter($page = null, $dept = null, $category = null){
      $session = $this->request->getSession();
      if ($session->check('filterData')) {
        $filterData = $session->read('filterData');
      }else {
        $filterData = array();
      }
      $filterData['page'] = $page;
      $filterData['dept'] = $dept;
      $filterData['category'] = $category;
      $session->write('filterData', $filterData);
    }

    public function getFilterData(){
      $session = $this->request->getSession();
      if ($session->check('filterData')) {
        return $session->read('filterData');
      }else {
        $filterData = array();
        $filterData['page'] = null;
        $filterData['dept'] = null;
        $filterData['category'] = null;
        return $filterData;
      }
    }

    public function getMomentPrevNext($filter, $current_id){
      $prev = 0;
      $next = 0;
      $moment_conditions = array();
      if (isset($filter['category'])) {
        $moment_conditions['item_id'] = $filter['category'];
      }else {
        $moment_conditions['item_id <> '] = '0';
      }
      if (isset($filter['dept'])) {
        $show_categories = 1;
        $moment_conditions['Items.dept'] = $filter['dept'];
      }
      $moments = TableRegistry::getTableLocator()->get('Articles')->find()->where($moment_conditions)->contain(['Items'])->toArray();
      foreach ($moments as $key => $moment) {
        if ($moment->id == $current_id) {
          if ($key != 0) {
            $prev = $moments[$key - 1]->id;
          }
          if (count($moments) != $key + 1) {
            $next = $moments[$key + 1]->id;
          }
          break;
        }
      }

      return ['prev'=>$prev, 'next'=>$next];
    }
}
