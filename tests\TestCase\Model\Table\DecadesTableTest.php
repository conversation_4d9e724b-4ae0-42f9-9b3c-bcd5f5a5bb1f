<?php
namespace App\Test\TestCase\Model\Table;

use App\Model\Table\DecadesTable;
use Cake\ORM\TableRegistry;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Table\DecadesTable Test Case
 */
class DecadesTableTest extends TestCase
{
    /**
     * Test subject
     *
     * @var \App\Model\Table\DecadesTable
     */
    public $Decades;

    /**
     * Fixtures
     *
     * @var array
     */
    public $fixtures = [
        'app.Decades',
        'app.Users',
        'app.Imgs',
    ];

    /**
     * setUp method
     *
     * @return void
     */
    public function setUp()
    {
        parent::setUp();
        $config = TableRegistry::getTableLocator()->exists('Decades') ? [] : ['className' => DecadesTable::class];
        $this->Decades = TableRegistry::getTableLocator()->get('Decades', $config);
    }

    /**
     * tearDown method
     *
     * @return void
     */
    public function tearDown()
    {
        unset($this->Decades);

        parent::tearDown();
    }

    /**
     * Test initialize method
     *
     * @return void
     */
    public function testInitialize()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test validationDefault method
     *
     * @return void
     */
    public function testValidationDefault()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test buildRules method
     *
     * @return void
     */
    public function testBuildRules()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }
}
