<?php
namespace App\Controller;

use App\Controller\AppController;
use Cake\Event\Event;
use Cake\ORM\TableRegistry;

class PerformanceController extends AppController
{
    public function beforeFilter(Event $event){
      $this->Auth->allow(['index']);
    }

    public function index()
    {
      $selected_decade = $this->request->getQuery('year');
      $selected_dept = $this->request->getQuery('dept');
      $conditions = array();
      if (isset($selected_decade) && $selected_decade != 0) {
        $conditions['decade_id'] = $selected_decade;
      }
      if (isset($selected_dept)) {
        $conditions['dept'] = $selected_dept;
      }
      $decades = TableRegistry::getTableLocator()->get('Decades')->find()->order(['year'=>'ASC']);
      $imgs = $this->paginate(TableRegistry::getTableLocator()->get('Imgs')->find()->where($conditions)->order(['created_at'=>'DESC']), ['maxLimit'=>1]);

      $this->set(compact('decades', 'imgs'));
    }
}
