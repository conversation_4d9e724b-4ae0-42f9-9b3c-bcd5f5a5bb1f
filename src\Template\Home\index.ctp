<?= $this->Html->css('Home/index.css') ?>
<?= $this->Html->script('Home/index.js') ?>
<?php $this->assign('title', __('首页'));?>

<!-- 电脑端布局 -->
<div class="home-pc">
  <div class="banner">
    <?=$this->Html->image('Home/home_gif.gif', ['alt'=>__('No Image'), 'style'=>'width: 100%;']);?>
  </div>
  <div class="row content-nav">
    <div class="col-md-6 left-side-style">
      <?=$this->Html->image('Home/left-side.png', [
        'alt'=>__('No Image'),
        'class'=>'row_img',
        'url'=>[
          'controller'=>'Moments',
          'action'=>'index'
        ]
      ]);?>
    </div>
    <div class="col-md-6 right-side-style">
      <?=$this->Html->image('Home/right-side.png', [
        'alt'=>__('No Image'),
        'class'=>'row_img',
        'url'=>[
          'controller'=>'Trial',
          'action'=>'index'
        ]
      ]);?>
    </div>
  </div>
  <div class="top-icon-wrapper">
    <div>
      <?=$this->Html->image('Home/icon-line.png', ['alt'=>__('No Image'), 'style'=>'width: 50%; margin-left: 10px;']);?>
    </div>
    <div>
      <?=$this->Html->image('Footer/wechat.png', ['class'=>'wechat-icon', 'alt'=>__('No Image'), 'style'=>'width: 100%;']);?>
    </div>
    <div>
      <a href="https://space.bilibili.com/496028447" target="_blank">
        <?=$this->Html->image('Footer/bilibili.png', ['alt'=>__('No Image'), 'style'=>'width: 100%;']);?>
      </a>
    </div>
    <div>
      <a href="https://www.zhihu.com/org/xin-ling-yu-li-gong-shu-49" target="_blank">
        <?=$this->Html->image('Footer/zhihu.png', ['alt'=>__('No Image'), 'style'=>'width: 100%; margin-left: 5px;']);?>
      </a>
    </div>
  </div>
</div>

<!-- paid+手机端布局 -->
<div class="home-phone">
  <div class="banner">
    <?=$this->Html->image('Home/home_gif.gif', ['alt'=>__('No Image'), 'style'=>'width: 100%;']);?>
  </div>

  <div class="icon-phone-container row content-nav mlr0">
    <div class="icon-phone-changewidth row mlr0" style="height:100%;">
      <div class="col pd0" style="height:100%;">
        <?=$this->Html->image('Footer/wechat.png', ['alt'=>__('No Image'), 'class'=>'icon-wechat icon-phone wechat-icon']);?>
      </div>
      <div class="col pd0" style="height:100%;">
        <a href="https://space.bilibili.com/496028447" target="_blank">
          <?=$this->Html->image('Footer/bilibili.png', ['alt'=>__('No Image'), 'class'=>'icon-bili icon-phone']);?>
        </a>
      </div>
      <div class="col pd0" style="height:100%;">
        <a href="https://www.zhihu.com/org/xin-ling-yu-li-gong-shu-49" target="_blank">
          <?=$this->Html->image('Footer/zhihu.png', ['alt'=>__('No Image'), 'class'=>'icon-zhihu  icon-phone']);?>
        </a>
      </div>
    </div>
  </div>

  <div class="row1-container row mlr0">
    <div class="col-4 pd0"></div>
    <div class="col-8 row mlr0 pd0">
      <div class="col pd0 item-center">
        <?=$this->Html->image('Home/row1-1.png', ['alt'=>__('No Image'), 
          'class'=>'btn-img',
          'url'=>['controller'=>'Moments', 'action'=>'index', 'dept'=>'0']]);?>
      </div>
      <div class="col pd0 item-center">
        <?=$this->Html->image('Home/row1-2.png', ['alt'=>__('No Image'), 'class'=>'btn-img',
          'url'=>['controller'=>'Moments', 'action'=>'index', 'dept'=>'1']]);?>
      </div>
      <div class="col pd0 item-center">
        <?=$this->Html->image('Home/row1-3.png', ['alt'=>__('No Image'), 'class'=>'btn-img',
          'url'=>['controller'=>'Moments','action'=>'index']]);?>
      </div>
    </div>
    <div class="col-4 pd0"></div>
      <div class="col-8 row mlr0 pd0">
        <div class="col pd0 item-center">
          <?=$this->Html->image('Home/row2-1.png', ['alt'=>__('No Image'), 
            'class'=>'btn-img',
            'url'=>['controller'=>'Trial', 'action'=>'index', 'dept'=>'0']]);?>
        </div>
        <div class="col pd0 item-center">
          <?=$this->Html->image('Home/row2-2.png', ['alt'=>__('No Image'), 'class'=>'btn-img',
            'url'=>['controller'=>'Trial', 'action'=>'index', 'dept'=>'1']]);?>
        </div>
        <div class="col pd0 item-center">
          <?=$this->Html->image('Home/row2-3.png', ['alt'=>__('No Image'), 'class'=>'btn-img',
            'url'=>['controller'=>'Trial', 'action'=>'index']]);?>
        </div>
      </div>
  </div>
    
</div>

<script>
$('.wechat-icon').popover({
  trigger: 'hover',
  placement: 'left',
  html: true,
  content: '<?=$this->Html->image("Footer/wechat-qr.png", ["class"=>"toggle-image rounded"]);?>',
});
</script>
