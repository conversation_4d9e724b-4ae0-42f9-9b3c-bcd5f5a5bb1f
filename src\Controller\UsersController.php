<?php
namespace App\Controller;

use App\Controller\AppController;
use Cake\I18n\Time;
use Cake\Event\Event;

/**
 * Users Controller
 *
 * @property \App\Model\Table\UsersTable $Users
 *
 * @method \App\Model\Entity\User[]|\Cake\Datasource\ResultSetInterface paginate($object = null, array $settings = [])
 */
class UsersController extends AppController
{
    public function beforeFilter(Event $event){
      $this->Auth->allow(['add']);
    }

    public function login(){
      if($this->request->is('post')){
        $user = $this->Auth->identify();
        if ($user) {
          $this->Auth->setUser($user);
          $this->Flash->success(__('Welcome '.str_replace('_', ' ', $user['name'])));
          return $this->redirect(['controller'=>'Home', 'action'=>'index']);
        } else {
          $this->Flash->error(__('用户名或者密码有误。请确认之后重新登录。'));
        }
      }
    }

    public function logout(){
      return $this->redirect($this->Auth->logout());
    }
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null
     */
    public function index()
    {
        $this->paginate = [
          'order'=>[
            'created_at'=>'DESC'
          ],
        ];
        $users = $this->paginate($this->Users);

        $this->set(compact('users'));
    }

    /**
     * View method
     *
     * @param string|null $id User id.
     * @return \Cake\Http\Response|null
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $user = $this->Users->get($id, [
            'contain' => [],
        ]);

        $this->set('user', $user);
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $user = $this->Users->newEntity();
        if ($this->request->is('post')) {
            $user = $this->Users->patchEntity($user, $this->request->getData());
            $user->password = "xly2021";
            $user->created_at = Time::now();
            $user->updated_at = Time::now();
            if ($this->Users->save($user)) {
                $this->Flash->success(__('创建用户成功。'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('创建用户失败。请联系系统管理员并稍后尝试。'));
        }
        $this->set(compact('user'));
    }

    /**
     * Edit method
     *
     * @param string|null $id User id.
     * @return \Cake\Http\Response|null Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $user = $this->Users->get($id, [
            'contain' => [],
        ]);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $user = $this->Users->patchEntity($user, $this->request->getData());
            $user->created_at = Time::now();
            if ($this->Users->save($user)) {
                $this->Flash->success(__('用户信息更新成功。'));

                return $this->redirect(['action' => 'view', $id]);
            }
            $this->Flash->error(__('用户信息更新失败。请联系系统管理员并稍后尝试。'));
        }
        $this->set(compact('user'));
    }

    /**
     * Delete method
     *
     * @param string|null $id User id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $user = $this->Users->get($id);
        if ($this->Users->delete($user)) {
            $this->Flash->success(__('删除用户成功。'));
        } else {
            $this->Flash->error(__('删除用户失败。请联系系统管理员并稍后尝试。'));
        }

        return $this->redirect(['action' => 'index']);
    }
}
