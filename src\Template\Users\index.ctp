<div>
  <h1><?=__('用户管理一览');?></h1>
  <hr>
  <?=$this->Html->link(__('新建用户'), ['action'=>'add'], [
    'class'=>'btn btn-success',
    'style'=>'width: 150px;'
  ]);?>
  <table class="table table-striped" style="margin-top: 30px;">
    <thead class="thead-dark">
      <tr>
        <th scope="col">#</th>
        <th scope="col"><?=__('用户名');?></th>
        <th scope="col"><?=__('姓名');?></th>
        <th scope="col"><?=__('系统权限');?></th>
        <th scope="col"><?=__('创建日期');?></th>
        <th scope="col"><?=__('操作选项');?></th>
      </tr>
    </thead>
    <tbody>
      <?php $count_num = 1; ?>
      <?php foreach ($users as $user): ?>
        <tr>
          <td><?=h($count_num);?></td>
          <td><?=h($user->username);?></td>
          <td><?=h($user->name);?></td>
          <td><?=h($user->role?__('一般用户'):__('管理员用户'));?></td>
          <td><?=h(date('Y-n-d H:i', strtotime($user->created_at)));?></td>
          <td>
            <?=$this->Html->link(__('详情'), ['action'=>'view', $user->id], [
              'class'=>'btn btn-info'
            ]);?>
            <?php if ($user->id != 1): ?>
              <?=$this->Form->postLink(__('删除'), ['action'=>'delete', $user->id], [
                'class'=>'btn btn-danger',
                'confirm'=>__('确定要删除[ {0} ]吗？', $user->username)
              ]);?>
            <?php endif; ?>
          </td>
        </tr>
        <?php $count_num = $count_num + 1; ?>
      <?php endforeach; ?>
    </tbody>
  </table>

  <div class="paginator">
      <ul class="pagination">
          <?= $this->Paginator->first('<< ' . __('first')) ?>
          <?= $this->Paginator->prev('< ' . __('上一页')) ?>
          <?= $this->Paginator->numbers() ?>
          <?= $this->Paginator->next(__('下一页') . ' >') ?>
          <?= $this->Paginator->last(__('last') . ' >>') ?>
      </ul>
      <p><?= $this->Paginator->counter(['format' => __('Page {{page}} of {{pages}}, showing {{current}} record(s) out of {{count}} total')]) ?></p>
  </div>
</div>
