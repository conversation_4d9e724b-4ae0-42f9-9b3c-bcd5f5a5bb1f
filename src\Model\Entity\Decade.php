<?php
namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Decade Entity
 *
 * @property int $id
 * @property int $user_id
 * @property int $year
 * @property string|null $description
 * @property \Cake\I18n\FrozenTime|null $created_at
 * @property \Cake\I18n\FrozenTime|null $updated_at
 *
 * @property \App\Model\Entity\User $user
 * @property \App\Model\Entity\Img[] $imgs
 */
class Decade extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array
     */
    protected $_accessible = [
        'user_id' => true,
        'year' => true,
        'description' => true,
        'created_at' => true,
        'updated_at' => true,
        'user' => true,
        'imgs' => true,
    ];
}
