<?php
namespace App\Controller;

use App\Controller\AppController;
use Cake\I18n\Time;

/**
 * Movies Controller
 *
 * @property \App\Model\Table\MoviesTable $Movies
 *
 * @method \App\Model\Entity\Movie[]|\Cake\Datasource\ResultSetInterface paginate($object = null, array $settings = [])
 */
class MoviesController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null
     */
    public function index()
    {
        $this->paginate = [
            'order'=>[
              'created_at'=>'DESC'
            ],
            'contain' => ['Users', 'Items'],
        ];
        $movies = $this->paginate($this->Movies);

        if ($this->request->is('post')) {
          $conditions = array();
          $title_key = $this->request->getData('title_key');
          $conditions['item_id'] = $this->request->getData('category');
          if (!empty($title_key)) {
            $conditions['OR']['title_ch LIKE '] = '%'.$title_key.'%';
            $conditions['OR']['title_jp LIKE '] = '%'.$title_key.'%';
          }

          $movies = $this->paginate($this->Movies->find()->where($conditions));
        }
        $itemTable = $this->Movies->Items;
        $items = $itemTable->find('list', [
          'keyField'=>'id',
          'valueField'=>function($itemTable){
            return $itemTable->get('label');
          }
        ])->where(['block'=>'2']);
        $this->set(compact('movies', 'items'));
    }

    /**
     * View method
     *
     * @param string|null $id Movie id.
     * @return \Cake\Http\Response|null
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $movie = $this->Movies->get($id, [
            'contain' => ['Users', 'Items'],
        ]);

        $this->set('movie', $movie);
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $movie = $this->Movies->newEntity();
        if ($this->request->is('post')) {
            $movie = $this->Movies->patchEntity($movie, $this->request->getData());
            $movie->user_id = $this->Auth->user('id');
            $movie->created_at = Time::now();
            $movie->updated_at = Time::now();
            if ($this->Movies->save($movie)) {
                $this->Flash->success(__('视频创建成功。'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('视频创建失败。请联系系统管理员并稍后尝试。'));
        }
        $itemTable = $this->Movies->Items;
        $items = $itemTable->find('list', [
          'keyField'=>'id',
          'valueField'=>function($itemTable){
            return $itemTable->get('label');
          }
        ])->where(['block'=>'2']);
        $this->set(compact('movie', 'items'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Movie id.
     * @return \Cake\Http\Response|null Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $movie = $this->Movies->get($id, [
            'contain' => [],
        ]);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $form_data = $this->request->getData();
            if (empty($form_data['banner']['size'])){
              unset($form_data['banner']);
            }
            if (empty($form_data['url']['size'])){
              unset($form_data['url']);
            }
            $movie = $this->Movies->patchEntity($movie, $form_data);
            $movie->updated_at = Time::now();
            if ($this->Movies->save($movie)) {
                $this->Flash->success(__('视频信息更新成功。'));

                return $this->redirect(['action' => 'view', $id]);
            }
            $this->Flash->error(__('视频信息更新失败。请联系系统管理员并稍后尝试。'));
        }
        $itemTable = $this->Movies->Items;
        $items = $itemTable->find('list', [
          'keyField'=>'id',
          'valueField'=>function($itemTable){
            return $itemTable->get('label');
          }
        ])->where(['block'=>'2']);
        $this->set(compact('movie', 'items'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Movie id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $movie = $this->Movies->get($id);
        if ($this->Movies->delete($movie)) {
            $this->Flash->success(__('视频删除成功。'));
        } else {
            $this->Flash->error(__('视频删除失败。请联系系统管理员并稍后尝试。'));
        }

        return $this->redirect(['action' => 'index']);
    }
}
