<div>
  <h1><?=__('师资管理一览');?></h1>
  <hr>
  <div class="row">
    <div class="col-md-2">
      <?=$this->Html->link(__('创建教师信息'), ['action'=>'add'], [
        'class'=>'btn btn-success',
        'style'=>'width: 150px;'
      ]);?>
    </div>
    <div class="col-md-2">
      <button class="btn btn-warning" style="width: 150px;" type="button" data-toggle="collapse" data-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
        <?=__('筛选条件');?>
      </button>
    </div>
  </div>
  <div class="filter-box">
    <div class="collapse" id="filterCollapse">
      <div class="card card-body">
        <?=$this->Form->create();?>
        <table class="table table-borderless">
          <tr>
            <td>
              <?=$this->Form->select('category_key', $items);?>
            </td>
            <td>
              <?=$this->Form->input('name_key', ['label'=>false, 'placeholder'=>__('请输入您要检索老师的姓名')])?>
            </td>
          </tr>
        </table>
        <div style="text-align: right;">
          <div class="d-inline-block">
            <?=$this->Form->submit(__('检索'), ['class'=>'btn btn-dark']);?>
          </div>
          <div class="d-inline-block">
            <?=$this->Html->link(__('重置'), ['action'=>'index'], ['class'=>'btn btn-secondary']);?>
          </div>
        </div>
        <?=$this->Form->end();?>
      </div>
    </div>
  </div>
  <table class="table table-striped" style="margin-top: 30px;">
    <thead class="thead-dark">
      <tr>
        <th scope="col" style="width: 15%;"><?=__('风采照片');?></th>
        <th scope="col" style="width: 10%;"><?=__('师资所属');?></th>
        <th scope="col" style="width: 15%;"><?=__('教师评分');?></th>
        <th scope="col" style="width: 20%;"><?=__('姓名(中/日)');?></th>
        <th scope="col" style="width: 15%;"><?=__('登录人');?></th>
        <th scope="col" style="width: 15%;"><?=__('创建日期');?></th>
        <th scope="col"><?=__('操作选项');?></th>
      </tr>
    </thead>
    <tbody>
      <?php $count_num = 1; ?>
      <?php foreach ($teachers as $teacher): ?>
        <tr>
          <td>
            <?php $image_url = ''; ?>
            <?php if (empty($teacher->url)): ?>
              <?php $image_url = 'Common/no_image.png'; ?>
            <?php else: ?>
              <?php $image_url = '../'.$teacher->url; ?>
            <?php endif; ?>
            <?=$this->Html->image($image_url, ['alt'=>__('No Image'), 'style'=>'width: 100%', 'class'=>'img-thumbnail']);?>
          </td>
          <td>
            <?php if (!empty($teacher->item->name_ch)): ?>
              <div><span class="badge badge-dark"><?=h($teacher->item->dept?__('大学院'):__('学部'));?></span></div>
              <div><span class="badge badge-info"><?=h($teacher->item->name_ch);?></span></div>
            <?php else: ?>
              <span class="badge badge-danger"><?=__('暂无分类');?></span>
            <?php endif; ?>
          </td>
          <td><?=h($teacher->rank);?></td>
          <td>
            <div>
              <span class="badge badge-dark" style="margin-right: 5px;"><?=__('中');?></span><?=h($teacher->name_ch);?>
            </div>
            <div>
              <span class="badge badge-dark" style="margin-right: 5px;"><?=__('日');?></span><?=h($teacher->name_jp);?>
            </div>
          </td>
          <td><?=h($teacher->user->username);?></td>
          <td><?=h(date('Y-n-d H:i', strtotime($teacher->created_at)));?></td>
          <td>
            <?=$this->Html->link(__('详情'), ['action'=>'view', $teacher->id], [
              'class'=>'btn btn-info'
            ]);?>
            <?=$this->Form->postLink(__('删除'), ['action'=>'delete', $teacher->id], [
              'class'=>'btn btn-danger',
              'confirm'=>__('确定要删除[ {0} ]吗？', $teacher->name_ch)
            ]);?>
          </td>
        </tr>
      <?php endforeach; ?>
    </tbody>
  </table>

  <div class="paginator">
      <ul class="pagination">
          <?= $this->Paginator->first('<< ' . __('first')) ?>
          <?= $this->Paginator->prev('< ' . __('上一页')) ?>
          <?= $this->Paginator->numbers() ?>
          <?= $this->Paginator->next(__('下一页') . ' >') ?>
          <?= $this->Paginator->last(__('last') . ' >>') ?>
      </ul>
      <p><?= $this->Paginator->counter(['format' => __('Page {{page}} of {{pages}}, showing {{current}} record(s) out of {{count}} total')]) ?></p>
  </div>
</div>

<style>
.filter-box {
  width: 50%;
  margin-top: 10px;
}
</style>
