<div>
  <h1><?=__('视频详情');?></h1>
  <hr>
  <?=$this->Html->link(__('更新视频信息'), ['action'=>'edit', $movie->id], [
    'class'=>'btn btn-warning',
    'style'=>'width: 150px;'
  ]);?>
  <table class="table table-striped" style="margin-top: 30px;">
    <thead class="thead-dark">
      <tr>
        <th style="width: 20%;"><?=__('项目');?></th>
        <th style="width: 40%;"><?=__('中文');?></th>
        <th style="width: 40%;"><?=__('日文');?></th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><?=__('视频分类');?></td>
        <td colspan="2">
          <?php if (!empty($movie->item->name_ch)): ?>
            <div><span class="badge badge-dark"><?=h($movie->item->dept?__('大学院'):__('学部'));?></span></div>
            <div><span class="badge badge-info"><?=h($movie->item->name_ch);?></span></div>
          <?php else: ?>
            <span class="badge badge-danger"><?=__('暂无分类');?></span>
          <?php endif; ?>
        </td>
      </tr>
      <tr>
        <td><?=__('视频内容');?></td>
        <td>
          <span class="badge badge-dark"><?=__('封面');?></span>
          <div>
            <?php $image_url = ''; ?>
            <?php if (empty($movie->banner)): ?>
              <?php $image_url = 'Common/no_image.png'; ?>
            <?php else: ?>
              <?php $image_url = '../'.$movie->banner; ?>
            <?php endif; ?>
            <?=$this->Html->image($image_url, ['alt'=>__('No Image'), 'style'=>'width: 80%', 'class'=>'img-thumbnail']);?>
          </div>
        </td>
        <td>
          <span class="badge badge-dark"><?=__('视频');?></span>
          <div>
            <?php if (empty($movie->url)): ?>
              <?=$this->Html->image('Common/no_video.png', ['alt'=>__('No Image'), 'style'=>'width: 80%', 'class'=>'img-thumbnail']);?>
            <?php else: ?>
              <?php $video_src_url = str_replace('files/', '', $movie->url); ?>
              <?=$this->Html->media($video_src_url, [
                'class'=>'figure-img img-fluid rounded',
                'preload'=>'auto',
                'controls' => true
              ]); ?>
            <?php endif; ?>
          </div>
        </td>
      </tr>
      <tr>
        <td><?=__('视频标题');?></td>
        <td><?=h($movie->title_ch);?></td>
        <td><?=h($movie->title_jp);?></td>
      </tr>
      <tr>
        <td><?=__('视频介绍');?></td>
        <td><?=$this->Text->autoParagraph(h($movie->description_ch));?></td>
        <td><?=$this->Text->autoParagraph(h($movie->description_jp));?></td>
      </tr>
    </tbody>
  </table>
  <div style="text-align: right">
    <?=$this->Html->link(__('返回'), ['action'=>'index'], [
      'class'=>'btn btn-secondary',
      'style'=>'width: 150px;'
    ]);?>
  </div>
</div>
