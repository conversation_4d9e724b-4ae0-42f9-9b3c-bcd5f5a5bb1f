<?php $current_lang = $this->getRequest()->getSession()->read('lang'); ?>
<div class="dept-box horizon-center">
  <div class="row" style="width: 250px;">
    <div class="col-4">
      <?=$this->Html->link(__('学部'), [
        'controller'=>$this->request->getParam('controller'),
        'action'=>'index',
        'dept'=>'0'
      ], [
        'style'=>($this->request->query('dept') != null && $this->request->query('dept') == 0)?'color: white;':'',
        'class'=>'a-dept-link'
      ]);?>
    </div>
    <div class="col-4" style="padding:0px;">
      <?=$this->Html->image('Aboutus/logo-w.png', [
        'alt'=>__('No Image'),
        'style'=>'width: 100%;',
        'url'=>[
          'controller'=>'Home',
          'action'=>'index'
        ]
      ]);?>
    </div>
    <div class="col-4">
      <?=$this->Html->link(__('大学院'), [
        'controller'=>$this->request->getParam('controller'),
        'action'=>'index',
        'dept'=>'1'
      ], [
        'style'=>($this->request->query('dept') == 1)?'color: white;':'',
        'class'=>'a-dept-link'
      ]);?>
    </div>
  </div>
</div>
<div class="mt-3">
  <?php foreach ($categories as $category): ?>
    <div class="detail-category-box">
      <strong>
        <?=$this->Html->link(($current_lang == 'ch')?$category->name_ch:$category->name_jp, [
          'controller'=>$this->request->getParam('controller'),
          'action'=>'index',
          'dept'=>$this->request->query('dept'),
          'category'=>$category->id
        ], [
          'class'=>'a-link',
          'style'=>($this->request->query('category')==$category->id)?'color: rgb(76, 110, 171); text-decoration: underline 5px;':''
        ]);?>
      </strong>
    </div>
  <?php endforeach; ?>
</div>
<style>
.dept-box {
  height: 55px;
  background: rgb(37, 57, 112);
  color: white;
  line-height: 50px;
}
.detail-category-box {
  display: inline-block;
  border-left: 4px solid rgb(37, 57, 112);
  border-right: 4px solid rgb(37, 57, 112);
  padding: 5px;
}
.a-dept-link {
  color: rgb(76, 110, 171);
  text-decoration: none;
}
.a-dept-link:hover {
  color: white;
  text-decoration: none;
}
.a-link {
  color: rgb(37, 57, 112);
  text-decoration: none;
}
.a-link:hover {
  color: rgb(76, 110, 171);
  text-decoration: none;
}
</style>
