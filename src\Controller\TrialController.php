<?php
namespace App\Controller;

use App\Controller\AppController;
use Cake\Event\Event;
use Cake\ORM\TableRegistry;

class TrialController extends AppController
{
    public function beforeFilter(Event $event){
      $this->Auth->allow(['index']);
    }

    public function index()
    {
      $current_lang = $this->Utility->get_current_lang();

      $dept = $this->request->getQuery('dept');
      $item = $this->request->getQuery('category');
      $show_categories = 0;

      $movie_conditions = array();
      if (isset($item)) {
        $movie_conditions['item_id'] = $item;
      }else {
        $movie_conditions['item_id <> '] = '0';
      }
      if (isset($dept)) {
        $show_categories = 1;
        $movie_conditions['Items.dept'] = $dept;
      }

      $categories = TableRegistry::getTableLocator()->get('Items')->find()->where(['block'=>'2', 'dept'=>$dept]);
      $movies = $this->paginate(TableRegistry::getTableLocator()->get('Movies')->find()->where($movie_conditions)->contain(['Items']));

      $this->set(compact('movies', 'show_categories', 'categories', 'current_lang'));
    }
}
