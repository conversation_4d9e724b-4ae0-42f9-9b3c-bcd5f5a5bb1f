<?php
namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Item Entity
 *
 * @property int $id
 * @property int $user_id
 * @property int $block
 * @property int $dept
 * @property string|null $url
 * @property string $name_ch
 * @property string $name_jp
 * @property string|null $description_ch
 * @property string|null $description_jp
 * @property \Cake\I18n\FrozenTime|null $created_at
 * @property \Cake\I18n\FrozenTime|null $updated_at
 *
 * @property \App\Model\Entity\User $user
 * @property \App\Model\Entity\Article[] $articles
 * @property \App\Model\Entity\Lesson[] $lessons
 * @property \App\Model\Entity\Movie[] $movies
 * @property \App\Model\Entity\Teacher[] $teachers
 */
class Item extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array
     */
    protected $_accessible = [
        'user_id' => true,
        'block' => true,
        'dept' => true,
        'url' => true,
        'name_ch' => true,
        'name_jp' => true,
        'description_ch' => true,
        'description_jp' => true,
        'created_at' => true,
        'updated_at' => true,
        'user' => true,
        'articles' => true,
        'lessons' => true,
        'movies' => true,
        'teachers' => true,
    ];

    protected function _getLabel(){
      $dept = $this->_properties['dept']?'大学院':'学部';
      return '( '.$dept. ' ) '.$this->_properties['name_ch'];
    }
}
