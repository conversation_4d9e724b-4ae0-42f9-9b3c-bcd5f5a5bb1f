<div>
  <h1><?=__('更新分类');?></h1>
  <hr>
  <?=$this->Form->create($item, ['type'=>'file']);?>
  <table class="table table-striped">
    <thead class="thead-dark">
      <tr>
        <th><?=__('项目');?></th>
        <th><?=__('中文');?></th>
        <th><?=__('日文');?></th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('分类内容所属');?></td>
        <td colspan="2">
          <?=$this->Form->select('block', [
            '0'=>__('课程分类'),
            '1'=>__('文章分类'),
            '2'=>__('视频分类'),
            '3'=>__('师资分类')
          ]);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('分类所属');?></td>
        <td colspan="2">
          <?=$this->Form->select('dept', [
            '0'=>__('学部'),
            '1'=>__('大学院')
          ]);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('分类封面');?></td>
        <td colspan="2">
          <?=$this->Form->input('url', ['label'=>false, 'type'=>'file']);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('分类名称');?></td>
        <td>
          <?=$this->Form->input('name_ch', ['label'=>false, 'placeholder'=>__('请输入中文分类名称')]);?>
        </td>
        <td>
          <?=$this->Form->input('name_jp', ['label'=>false, 'placeholder'=>__('请输入日文分类名称')]);?>
        </td>
      </tr>
      <tr>
        <td><?=__('分类备注');?></td>
        <td>
          <?=$this->Form->textarea('description_ch', ['label'=>false, 'placeholder'=>__('请输入中文分类备注'), 'style'=>'height: 350px;']);?>
        </td>
        <td>
          <?=$this->Form->textarea('description_jp', ['label'=>false, 'placeholder'=>__('请输入日文分类备注'), 'style'=>'height: 350px;']);?>
        </td>
      </tr>
    </tbody>
  </table>
  <div style="text-align: right">
    <div style="display: inline-block">
      <?=$this->Form->submit(__('确定'), [
        'class'=>'btn btn-success',
        'style'=>'width: 150px;'
      ]);?>
    </div>
    <div style="display: inline-block">
      <?=$this->Html->link(__('返回'), $this->request->referer(), [
        'class'=>'btn btn-secondary',
        'style'=>'width: 150px;'
      ]);?>
    </div>
  </div>
  <?=$this->Form->end();?>
</div>
