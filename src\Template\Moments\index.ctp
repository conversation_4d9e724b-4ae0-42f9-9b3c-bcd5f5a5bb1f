<?= $this->Html->css('Moment/index.css') ?>
<?php $this->start('script'); ?>
<?= $this->Html->script('common/page.js') ?>
<?php $this->end(); ?>
<?php $this->assign('title', __('最新动态'));?>
<div class="text-color">
  <?=$this->element('filter', ['categories'=>$categories]);?>
  <hr>
  <div>
    <div class="row">
      <?php foreach ($moments as $key => $moment): ?>
        <div class="col-md-4">
          <div class="card card-border-style">
            <?php $moment_url = ''; ?>
            <?php if (empty($moment->banner)): ?>
              <?php $moment_url = 'Common/no_image.png'; ?>
            <?php else: ?>
              <?php $moment_url = '../'.$moment->banner; ?>
            <?php endif; ?>
            <div class="moment-card-image">
              <?=$this->Html->image($moment_url, [
                'alt'=>__('No Image'),
                'class'=>'card-image',
                'url'=>[
                  'action'=>'momentDetail', $moment->id
                ]
              ]);?>
            </div>
            <div class="card-body news-text">
              <div><span class="badge badge-info"><?=h(($current_lang == 'ch')?$moment->item->name_ch:$moment->item->name_jp);?></span></div>
              <p class="card-text"><?=h($moment->title);?></p>
            </div>
          </div>
        </div>
      <?php endforeach; ?>
    </div>
    <div class="paginator mt-5 horizon-center">
        <ul class="pagination">
            <?= $this->Paginator->first('<< ' . __('first')) ?>
            <?= $this->Paginator->prev('< ' . __('上一页')) ?>
            <?= $this->Paginator->numbers() ?>
            <?= $this->Paginator->next(__('下一页') . ' >') ?>
            <?= $this->Paginator->last(__('last') . ' >>') ?>
        </ul>
    </div>
  </div>
</div>
<style>
.card-body{
    overflow-y: scroll;
    overflow: -moz-scrollbars-none;/* 旧版本firefox对应 */
    -ms-overflow-style: none; /* IE 10+对应 */
    scrollbar-width: none; /* 最新版本firefox对应 */
}
.card-body::-webkit-scrollbar { 
    display: none; /* chrome和safari对应 */
    width: 0 !important
}
</style>