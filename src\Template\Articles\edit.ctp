<div>
  <h1><?=__('更新文章信息');?></h1>
  <hr>
  <?=$this->Form->create($article, ['type'=>'file']);?>
  <table class="table table-striped">
    <tbody>
      <tr>
        <td style="width: 25%;"><?=$this->element('star', ['color'=>'red']);?><?=__('文章分类');?></td>
        <td>
          <?=$this->Html->link(__('新建分类'), ['controller'=>'Items', 'action'=>'add', $article->id, 'block'=>'1', 'mode'=>'edit'], [
            'class'=>'btn btn-success', 'style'=>'margin-bottom: 10px;'
          ]);?>
          <?=$this->Form->select('item_id', $items);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('文章封面');?></td>
        <td>
          <?=$this->Form->input('banner', ['label'=>false, 'type'=>'file', 'id'=>'img-selector']);?>
          <?php $image_url = ''; ?>
          <?php if (empty($article->banner)): ?>
            <?php $image_url = 'Common/no_image.png'; ?>
          <?php else: ?>
            <?php $image_url = '../'.$article->banner; ?>
          <?php endif; ?>
          <?=$this->Html->image($image_url, ['alt'=>__('No Image'), 'style'=>'width: 50%', 'class'=>'img-thumbnail', 'id'=>'img-preview']);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('文章标题');?></td>
        <td>
          <?=$this->Form->input('title', ['label'=>false, 'placeholder'=>__('请输入文章标题')]);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('文章内容');?></td>
        <td>
          <?=$this->Form->textarea('content', ['label'=>false, 'placeholder'=>__('请输入文章内容'), 'style'=>'height: 350px;']);?>
        </td>
      </tr>
    </tbody>
  </table>
  <div style="text-align: right">
    <div style="display: inline-block">
      <?=$this->Form->submit(__('确定'), [
        'class'=>'btn btn-success',
        'style'=>'width: 150px;'
      ]);?>
    </div>
    <div style="display: inline-block">
      <?=$this->Html->link(__('返回'), ['action'=>'view', $article->id], [
        'class'=>'btn btn-secondary',
        'style'=>'width: 150px;'
      ]);?>
    </div>
  </div>
  <?=$this->Form->end();?>
</div>
<script>
$(document).ready(function(){
  CKEDITOR.replace('content', {
    filebrowserUploadUrl: '<?=$this->Url->build(['action'=>'upload']);?>',
    filebrowserUploadMethod: 'form',
  });
});

$('#img-selector').change(function(){
  $('#img-preview').attr('src',URL.createObjectURL($(this)[0].files[0]));
});
</script>
