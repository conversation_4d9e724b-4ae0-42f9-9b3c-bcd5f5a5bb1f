<div>
  <h1><?=__('新建师资');?></h1>
  <hr>
  <?=$this->Form->create($teacher, ['type'=>'file']);?>
  <table class="table table-striped">
    <thead class="thead-dark">
      <tr>
        <th><?=__('项目');?></th>
        <th><?=__('中文');?></th>
        <th><?=__('日文');?></th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('风采照片');?></td>
        <td colspan="2">
          <?=$this->Form->input('url', ['label'=>false, 'type'=>'file']);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('师资所属');?></td>
        <td colspan="2">
          <?=$this->Html->link(__('新建分类'), ['controller'=>'Items', 'action'=>'add', 'block'=>'3', 'mode'=>'add'], [
            'class'=>'btn btn-success', 'style'=>'margin-bottom: 10px;'
          ]);?>
          <?=$this->Form->select('item_id', $items);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('教师姓名');?></td>
        <td>
          <?=$this->Form->input('name_ch', ['label'=>false, 'placeholder'=>__('请输入中文教师姓名')]);?>
        </td>
        <td>
          <?=$this->Form->input('name_jp', ['label'=>false, 'placeholder'=>__('请输入日文教师姓名')]);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('教师介绍');?></td>
        <td>
          <?=$this->Form->textarea('description_ch', ['label'=>false, 'placeholder'=>__('请输入中文教师介绍'), 'style'=>'height: 350px;']);?>
        </td>
        <td>
          <?=$this->Form->textarea('description_jp', ['label'=>false, 'placeholder'=>__('请输入日文教师介绍'), 'style'=>'height: 350px;']);?>
        </td>
      </tr>
      <tr>
        <td><?=__('教师评分');?></td>
        <td colspan="2">
          <?=$this->Form->input('rank', ['label'=>false, 'placeholder'=>__('请输入教师评分')]);?>
        </td>
      </tr>
    </tbody>
  </table>
  <div style="text-align: right">
    <div style="display: inline-block">
      <?=$this->Form->submit(__('确定'), [
        'class'=>'btn btn-success',
        'style'=>'width: 150px;'
      ]);?>
    </div>
    <div style="display: inline-block">
      <?=$this->Html->link(__('返回'), ['action'=>'index'], [
        'class'=>'btn btn-secondary',
        'style'=>'width: 150px;'
      ]);?>
    </div>
  </div>
  <?=$this->Form->end();?>
</div>
