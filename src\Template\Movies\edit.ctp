<div>
  <h1><?=__('更新视频信息');?></h1>
  <hr>
  <?=$this->Form->create($movie, ['type'=>'file']);?>
  <table class="table table-striped">
    <thead class="thead-dark">
      <tr>
        <th><?=__('项目');?></th>
        <th><?=__('中文');?></th>
        <th><?=__('日文');?></th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td style="width: 25%;"><?=$this->element('star', ['color'=>'red']);?><?=__('文章分类');?></td>
        <td colspan="2">
          <?=$this->Html->link(__('新建分类'), ['controller'=>'Items', 'action'=>'add', $movie->id, 'block'=>'2', 'mode'=>'edit'], [
            'class'=>'btn btn-success', 'style'=>'margin-bottom: 10px;'
          ]);?>
          <?=$this->Form->select('item_id', $items);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('封面图片');?></td>
        <td colspan="2">
          <?=$this->Form->input('banner', ['label'=>false, 'type'=>'file', 'id'=>'img-selector']);?>
          <?php $image_url = ''; ?>
          <?php if (empty($movie->banner)): ?>
            <?php $image_url = 'Common/no_image.png'; ?>
          <?php else: ?>
            <?php $image_url = '../'.$movie->banner; ?>
          <?php endif; ?>
          <?=$this->Html->image($image_url, ['alt'=>__('No Image'), 'style'=>'width: 50%', 'class'=>'img-thumbnail', 'id'=>'img-preview']);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('视频文件');?></td>
        <td colspan="2">
          <?=$this->Form->input('url', ['label'=>false, 'type'=>'file']);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('视频标题');?></td>
        <td>
          <?=$this->Form->input('title_ch', ['label'=>false, 'placeholder'=>__('请输入中文标题')]);?>
        </td>
        <td>
          <?=$this->Form->input('title_jp', ['label'=>false, 'placeholder'=>__('请输入日文标题')]);?>
        </td>
      </tr>
      <tr>
        <td><?=$this->element('star', ['color'=>'red']);?><?=__('视频详情介绍');?></td>
        <td>
          <?=$this->Form->textarea('description_ch', ['label'=>false, 'placeholder'=>__('请输入中文视频详细介绍'), 'style'=>'height: 350px;']);?>
        </td>
        <td>
          <?=$this->Form->textarea('description_jp', ['label'=>false, 'placeholder'=>__('请输入日文视频详细介绍'), 'style'=>'height: 350px;']);?>
        </td>
      </tr>
    </tbody>
  </table>
  <div style="text-align: right">
    <div style="display: inline-block">
      <?=$this->Form->submit(__('更新'), [
        'class'=>'btn btn-success',
        'style'=>'width: 150px;'
      ]);?>
    </div>
    <div style="display: inline-block">
      <?=$this->Html->link(__('返回'), ['action'=>'view', $movie->id], [
        'class'=>'btn btn-secondary',
        'style'=>'width: 150px;'
      ]);?>
    </div>
  </div>
  <?=$this->Form->end();?>
</div>
<script>
$('#img-selector').change(function(){
  $('#img-preview').attr('src',URL.createObjectURL($(this)[0].files[0]));
});
</script>
