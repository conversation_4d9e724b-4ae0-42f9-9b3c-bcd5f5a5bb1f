<?php
namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Img Entity
 *
 * @property int $id
 * @property int $decade_id
 * @property int $dept
 * @property string|null $url
 * @property \Cake\I18n\FrozenTime|null $created_at
 * @property \Cake\I18n\FrozenTime|null $updated_at
 *
 * @property \App\Model\Entity\Decade $decade
 */
class Img extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array
     */
    protected $_accessible = [
        'decade_id' => true,
        'dept' => true,
        'url' => true,
        'created_at' => true,
        'updated_at' => true,
        'decade' => true,
    ];
}
