<div>
  <h1><?=__('分类管理一览');?></h1>
  <hr>
  <div class="row">
    <div class="col-md-2">
      <?=$this->Html->link(__('新建分类'), ['action'=>'add'], [
        'class'=>'btn btn-success',
        'style'=>'width: 150px;'
      ]);?>
    </div>
    <div class="col-md-2">
      <button class="btn btn-warning" style="width: 150px;" type="button" data-toggle="collapse" data-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
        <?=__('筛选条件');?>
      </button>
    </div>
  </div>
  <div class="filter-box">
    <div class="collapse" id="filterCollapse">
      <div class="card card-body">
        <?=$this->Form->create();?>
        <table class="table table-borderless">
          <tr>
            <td>
              <?=$this->Form->select('block_key', [
                'all'=>__('全部'),
                '0'=>__('课程分类'),
                '1'=>__('文章分类'),
                '2'=>__('视频分类'),
                '3'=>__('师资分类')
              ]);?>
            </td>
            <td>
              <?=$this->Form->select('dept_key', [
                'all'=>__('全部'),
                '0'=>__('学部'),
                '1'=>__('大学院')
              ]);?>
            </td>
            <td>
              <?=$this->Form->input('name_key', ['label'=>false, 'placeholder'=>__('请输入您要检索分类的名称')])?>
            </td>
          </tr>
        </table>
        <div style="text-align: right;">
          <div class="d-inline-block">
            <?=$this->Form->submit(__('检索'), ['class'=>'btn btn-dark']);?>
          </div>
          <div class="d-inline-block">
            <?=$this->Html->link(__('重置'), ['action'=>'index'], ['class'=>'btn btn-secondary']);?>
          </div>
        </div>
        <?=$this->Form->end();?>
      </div>
    </div>
  </div>
  <table class="table table-striped" style="margin-top: 30px;">
    <thead class="thead-dark">
      <tr>
        <th scope="col">#</th>
        <th scope="col" style="width: 10%;"><?=__('分类所属');?></th>
        <th scope="col" style="width: 20%;"><?=__('分类图片');?></th>
        <th scope="col" style="width: 20%;"><?=__('分类名(中/日)');?></th>
        <th scope="col" style="width: 10%;"><?=__('登录人');?></th>
        <th scope="col" style="width: 15%;"><?=__('创建日期');?></th>
        <th scope="col"><?=__('操作选项');?></th>
      </tr>
    </thead>
    <tbody>
      <?php $count_number = 1; ?>
      <?php foreach ($items as $item): ?>
        <tr>
          <td><?=h($count_number);?></td>
          <td>
            <div>
              <span class="badge badge-dark">
                <?php if ($item->block == 0): ?>
                  <?=__('课程分类');?>
                <?php endif; ?>
                <?php if ($item->block == 1): ?>
                  <?=__('文章分类');?>
                <?php endif; ?>
                <?php if ($item->block == 2): ?>
                  <?=__('视频分类');?>
                <?php endif; ?>
                <?php if ($item->block == 3): ?>
                  <?=__('师资分类');?>
                <?php endif; ?>
              </span>
            </div>
            <div>
              <span class="badge badge-info">
                <?=h($item->dept?__('大学院'):__('学部'));?>
              </span>
            </div>
          </td>
          <td>
            <?php $image_url = ''; ?>
            <?php if (empty($item->url)): ?>
              <?php $image_url = 'Common/no_image.png'; ?>
            <?php else: ?>
              <?php $image_url = '../'.$item->url; ?>
            <?php endif; ?>
            <?=$this->Html->image($image_url, ['alt'=>__('No Image'), 'style'=>'width: 100%', 'class'=>'img-thumbnail']);?>
          </td>
          <td>
            <div>
              <span class="badge badge-dark" style="margin-right: 5px;"><?=__('中');?></span><?=h($item->name_ch);?>
            </div>
            <div>
              <span class="badge badge-dark" style="margin-right: 5px;"><?=__('日');?></span><?=h($item->name_jp);?>
            </div>
          </td>
          <td><?=h($item->user->username);?></td>
          <td><?=h(date('Y-m-d H:i', strtotime($item->created_at)));?></td>
          <td>
            <?=$this->Html->link(__('更新'), ['action'=>'edit', $item->id], [
              'class'=>'btn btn-warning'
            ]);?>
            <?=$this->Form->postLink(__('删除'), ['action'=>'delete', $item->id], [
              'class'=>'btn btn-danger',
              'confirm'=>__('确定要删除[ {0} ]吗？', $item->name_ch)
            ]);?>
          </td>
        </tr>
        <?php $count_number = $count_number + 1; ?>
      <?php endforeach; ?>
    </tbody>
  </table>
  <div class="paginator">
      <ul class="pagination">
          <?= $this->Paginator->first('<< ' . __('first')) ?>
          <?= $this->Paginator->prev('< ' . __('上一页')) ?>
          <?= $this->Paginator->numbers() ?>
          <?= $this->Paginator->next(__('下一页') . ' >') ?>
          <?= $this->Paginator->last(__('last') . ' >>') ?>
      </ul>
      <p><?= $this->Paginator->counter(['format' => __('Page {{page}} of {{pages}}, showing {{current}} record(s) out of {{count}} total')]) ?></p>
  </div>
</div>

<style>
.filter-box {
  width: 50%;
  margin-top: 10px;
}
</style>
