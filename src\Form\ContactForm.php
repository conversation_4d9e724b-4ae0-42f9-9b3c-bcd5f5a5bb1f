<?php
namespace App\Form;

use Cake\Form\Form;
use Cake\Form\Schema;
use Cake\Validation\Validator;

/**
 * Contact Form.
 */
class ContactForm extends Form
{
    /**
     * Builds the schema for the modelless form
     *
     * @param \Cake\Form\Schema $schema From schema
     * @return \Cake\Form\Schema
     */
    protected function _buildSchema(Schema $schema)
    {
        $schema->addField('name', 'string')
               ->addField('tel', ['type' => 'string'])
               ->addField('wechat', ['type' => 'string'])
               ->addField('mail', ['type' => 'string'])
               ->addField('content', ['type' => 'text']);

        return $schema;
    }

    /**
     * Form validation builder
     *
     * @param \Cake\Validation\Validator $validator to use against the form
     * @return \Cake\Validation\Validator
     */
    protected function _buildValidator(Validator $validator)
    {
        $validator
          ->scalar('name')
          ->maxLength('name', 255)
          ->requirePresence('name', 'create')
          ->notEmptyString('name', __('请输入您的姓名/公司名称/组织名称'));

        $validator
          ->scalar('tel')
          ->maxLength('tel', 255)
          ->requirePresence('tel', 'create')
          ->notEmptyString('tel', __('请输入您的联系电话'));

        $validator
          ->scalar('wechat')
          ->maxLength('wechat', 255)
          ->requirePresence('wechat', 'create')
          ->notEmptyString('wechat', __('请输入可以联系到您的微信号'));

        $validator
          ->scalar('mail')
          ->maxLength('mail', 255)
          ->requirePresence('mail', 'create')
          ->allowEmptyString('mail');

        $validator
          ->scalar('content')
          ->maxLength('content', 4294967295)
          ->requirePresence('content', 'create')
          ->notEmptyString('content', __('请输入您要咨询的内容'));

        return $validator;
    }

    /**
     * Defines what to execute once the Form is processed
     *
     * @param array $data Form data.
     * @return bool
     */
    protected function _execute(array $data)
    {
        return $data;
    }
}
