<?= $this->Html->script('Trial/index.js') ?>
<?php $this->start('script'); ?>
<?= $this->Html->script('common/page.js') ?>
<?php $this->end(); ?>
<?= $this->Html->css('Trial/index.css') ?>
<?php $this->assign('title', __('课程试听'));?>
<div class="text-color">
  <?=$this->element('filter', ['categories'=>$categories]);?>
  <hr>
  <div>
    <div class="row">
      <?php foreach ($movies as $movie): ?>
        <div class="col-md-4">
          <div class="card card-border-style">
            <?php $movie_banner_url = ''; ?>
            <?php if (empty($movie->banner)): ?>
              <?php $movie_banner_url = 'Common/no_image.png'; ?>
            <?php else: ?>
              <?php $movie_banner_url = '../'.$movie->banner; ?>
            <?php endif; ?>
            <?php $movie_description = str_replace(array("\r\n", "\r", "\n"), "<br>", ($current_lang == 'ch')?$movie->description_ch:$movie->description_jp); ?>
            <?php $movie_title = ($current_lang == 'ch')?$movie->title_ch:$movie->title_jp; ?>
            <div class="video-card-image">
              <?=$this->Html->image($movie_banner_url, [
                'alt'=>__('No Image'),
                'class'=>'card-image',
                'onclick'=>'showMovieModal("'.$movie_title.'", "'.$movie->url.'", "'.$movie_description.'")'
              ]);?>
            </div>
            <div class="card-body news-text">
              <div><span class="badge badge-info"><?=h(($current_lang == 'ch')?$movie->item->name_ch:$movie->item->name_jp);?></span></div>
              <p class="card-text"><?=h(($current_lang == 'ch')?$movie->title_ch:$movie->title_jp);?></p>
            </div>
          </div>
        </div>
      <?php endforeach; ?>
    </div>
    <div class="paginator mt-5 horizon-center">
        <ul class="pagination">
            <?= $this->Paginator->first('<< ' . __('first')) ?>
            <?= $this->Paginator->prev('< ' . __('上一页')) ?>
            <?= $this->Paginator->numbers(['class'=>'page-link']) ?>
            <?= $this->Paginator->next(__('下一页') . ' >') ?>
            <?= $this->Paginator->last(__('last') . ' >>') ?>
        </ul>
    </div>
  </div>

  <div class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-labelledby="staticBackdropLabel" aria-hidden="true" id="movieModal">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="movieName"></h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <video id="moviePlayer" controls="controls" style="width: 100%;"></video>
          <p id="movieDescription"></p>
        </div>
      </div>
    </div>
  </div>
</div>
<style>
.card-body{
    overflow-y: scroll;
    overflow: -moz-scrollbars-none;/* 旧版本firefox对应 */
    -ms-overflow-style: none; /* IE 10+对应 */
    scrollbar-width: none; /* 最新版本firefox对应 */
}
.card-body::-webkit-scrollbar { 
    display: none; /* chrome和safari对应 */
    width: 0 !important
}
</style>