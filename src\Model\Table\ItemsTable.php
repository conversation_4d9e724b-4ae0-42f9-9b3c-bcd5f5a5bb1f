<?php
namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * Items Model
 *
 * @property \App\Model\Table\UsersTable&\Cake\ORM\Association\BelongsTo $Users
 * @property \App\Model\Table\ArticlesTable&\Cake\ORM\Association\HasMany $Articles
 * @property \App\Model\Table\LessonsTable&\Cake\ORM\Association\HasMany $Lessons
 * @property \App\Model\Table\MoviesTable&\Cake\ORM\Association\HasMany $Movies
 * @property \App\Model\Table\TeachersTable&\Cake\ORM\Association\HasMany $Teachers
 *
 * @method \App\Model\Entity\Item get($primaryKey, $options = [])
 * @method \App\Model\Entity\Item newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\Item[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Item|false save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\Item saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\Item patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\Item[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\Item findOrCreate($search, callable $callback = null, $options = [])
 */
class ItemsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('items');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Utils.Uploadable', [
          'url'=>[
            'field'=>'id',
            'path' => '{ROOT}{DS}{WEBROOT}{DS}files{DS}items{DS}{field}{DS}',
            'fileName' => date('YmdHis').'_item'.'.{extension}',
            'removeFileOnDelete' => true,
            'removeFileOnUpdate' => true,
          ]
        ]);

        $this->belongsTo('Users', [
            'foreignKey' => 'user_id',
            'joinType' => 'INNER',
        ]);
        $this->hasMany('Articles', [
            'foreignKey' => 'item_id',
        ]);
        $this->hasMany('Lessons', [
            'foreignKey' => 'item_id',
        ]);
        $this->hasMany('Movies', [
            'foreignKey' => 'item_id',
        ]);
        $this->hasMany('Teachers', [
            'foreignKey' => 'item_id',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->integer('id')
            ->allowEmptyString('id', null, 'create');

        $validator
            ->integer('block')
            ->requirePresence('block', 'create')
            ->notEmptyString('block');

        $validator
            ->integer('dept')
            ->requirePresence('dept', 'create')
            ->notEmptyString('dept');

        $validator
            ->allowEmptyString('url');

        $validator
            ->scalar('name_ch')
            ->maxLength('name_ch', 255)
            ->requirePresence('name_ch', 'create')
            ->notEmptyString('name_ch');

        $validator
            ->scalar('name_jp')
            ->maxLength('name_jp', 255)
            ->requirePresence('name_jp', 'create')
            ->notEmptyString('name_jp');

        $validator
            ->scalar('description_ch')
            ->maxLength('description_ch', 4294967295)
            ->allowEmptyString('description_ch');

        $validator
            ->scalar('description_jp')
            ->maxLength('description_jp', 4294967295)
            ->allowEmptyString('description_jp');

        $validator
            ->dateTime('created_at')
            ->allowEmptyDateTime('created_at');

        $validator
            ->dateTime('updated_at')
            ->allowEmptyDateTime('updated_at');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules)
    {
        $rules->add($rules->existsIn(['user_id'], 'Users'));

        return $rules;
    }
}
