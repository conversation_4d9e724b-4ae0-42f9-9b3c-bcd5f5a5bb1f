<?php
/**
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright     Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link          https://cakephp.org CakePHP(tm) Project
 * @since         0.10.0
 * @license       https://opensource.org/licenses/mit-license.php MIT License
 * @var \App\View\AppView $this
 */

$cakeDescription = __('新领域理工塾');
?>
<!DOCTYPE html>
<html>
<head>
    <?= $this->Html->charset() ?>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache">
    <title>
        <?= $cakeDescription ?>:
        <?= $this->fetch('title') ?>
    </title>
    <?= $this->Html->meta('icon') ?>

    <?= $this->Html->script('jquery-3.6.0.min.js') ?>
    <?= $this->Html->script('popper.min.js') ?>
    <?= $this->Html->script('../ckeditor/ckeditor.js') ?>
    <?= $this->Html->script('../bootstrap/js/bootstrap.js') ?>
    <?= $this->Html->css('../bootstrap/css/bootstrap.css') ?>
    <?= $this->Html->css('../bootstrap/icons/font/bootstrap-icons.css') ?>
    <?= $this->Html->css('wrapper.css') ?>

    <?= $this->fetch('meta') ?>
    <?= $this->fetch('css') ?>
    <?= $this->fetch('script') ?>
</head>
<body>
    <?= $this->element('header') ?>
    <div class="clearfix wrapper">
        <?= $this->Flash->render() ?>
        <?= $this->fetch('content') ?>
        <?= $this->element('uptotop') ?>
    </div>
    <?= $this->element('footer') ?>
</body>
</html>
