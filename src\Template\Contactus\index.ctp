<?= $this->Html->css('Contactus/index.css') ?>
<?php $this->assign('title', __('联系我们'));?>
<div class="text-color">
  <div class="row">
    <div class="col-md-6 img-block">
      <?=$this->Html->image('Contactus/map-left.png', ['alt'=>__('No Image'), 'class'=>'left-img']);?>
      <?=$this->Html->image('Contactus/map-right.png', ['alt'=>__('No Image'), 'class'=>'right-img']);?>
    </div>
    <div class="col-md-6">
      <?=$this->Form->create($contact);?>
      <table class="table text-color customize-table">
        <tr>
          <td style="width: 25%;"><strong><?=$this->element('star');?><?=__('姓名');?></strong></td>
          <td>
            <?=$this->Form->input('name', ['class'=>'input-wrapper', 'label'=>false, 'required'=>false, 'placeholder'=>__('请输入您的姓名(或者公司名或者组织名称)')]);?>
          </td>
        </tr>
        <tr>
          <td rowspan="3"><strong><span ></span><?=__('联系方式');?></strong></td>
          <td>
            <div><?=$this->element('star');?><?=__('联系电话');?></div>
            <?=$this->Form->input('tel', ['class'=>'input-wrapper', 'label'=>false, 'required'=>false, 'placeholder'=>__('请输入您的联系电话')]);?>
          </td>
        </tr>
        <tr>
          <td>
            <div><?=$this->element('star');?><?=__('微信号/QQ号');?></div>
            <?=$this->Form->input('wechat', ['class'=>'input-wrapper', 'label'=>false, 'required'=>false, 'placeholder'=>__('请输入您的微信号或QQ号')]);?>
          </td>
        </tr>
        <tr>
          <td>
            <div><?=__('邮箱');?></div>
            <?=$this->Form->input('mail', ['class'=>'input-wrapper', 'label'=>false, 'required'=>false, 'placeholder'=>__('请输入您的邮箱')]);?>
          </td>
        </tr>
        <tr>
          <td><strong><?=$this->element('star');?><?=__('咨询内容');?></strong></td>
          <td>
            <?=$this->Form->textarea('content', ['class'=>'input-wrapper', 'label'=>false, 'required'=>false, 'placeholder'=>__('请输入您想要咨询的内容')]);?>
            <?=$this->Form->error('content');?>
          </td>
        </tr>
      </table>
      <div style="text-align: center; margin-top: 20px;">
        <?=$this->Form->submit(__('咨询'), ['class'=>'btn btn-primary', 'style'=>'width: 150px;background-color:rgb(37, 57, 112);font-weight:bold']);?>
      </div>
      <?=$this->Form->end();?>
    </div>
  </div>
</div>
