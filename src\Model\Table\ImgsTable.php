<?php
namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * Imgs Model
 *
 * @property \App\Model\Table\DecadesTable&\Cake\ORM\Association\BelongsTo $Decades
 *
 * @method \App\Model\Entity\Img get($primaryKey, $options = [])
 * @method \App\Model\Entity\Img newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\Img[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Img|false save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\Img saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\Img patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\Img[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\Img findOrCreate($search, callable $callback = null, $options = [])
 */
class ImgsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('imgs');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Utils.Uploadable', [
          'url'=>[
            'field'=>'decade_id',
            'path' => '{ROOT}{DS}{WEBROOT}{DS}files{DS}performances{DS}{field}{DS}',
            'fileName' => date('YmdHis').'_performance'.'.{extension}',
            'removeFileOnDelete' => true,
            'removeFileOnUpdate' => true,
          ]
        ]);

        $this->belongsTo('Decades', [
            'foreignKey' => 'decade_id',
            'joinType' => 'INNER',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->integer('id')
            ->allowEmptyString('id', null, 'create');

        $validator
            ->integer('dept')
            ->requirePresence('dept', 'create')
            ->notEmptyString('dept');

        $validator
            ->allowEmptyString('url');

        $validator
            ->dateTime('created_at')
            ->allowEmptyDateTime('created_at');

        $validator
            ->dateTime('updated_at')
            ->allowEmptyDateTime('updated_at');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules)
    {
        $rules->add($rules->existsIn(['decade_id'], 'Decades'));

        return $rules;
    }
}
