<?= $this->Html->css('Ourteachers/index.css') ?>
<?= $this->Html->script('Ourteachers/index.js') ?>
<?php $this->assign('title', __('师资力量'));?>
<div class="text-color">
  <?=$this->element('filter', ['categories'=>$categories]);?>
  <hr>
  <div class="horizon-center mt-5 mb-5">
    <div class="teacher-description-box">
      <?php if ($dept == 0): ?>
        <p>
          <?=__('学部各科均由取得过EJU高分、在所处专业出类拔萃的讲师进行授课。同时，新领域理工塾不仅注重讲师的个人水平，对于讲师的授课方法、态度及跟进学生学习状况等综合教学质量均有高水平要求。我们的讲师团队实力雄厚且耐心负责，得益于此我们可以保障：所有同学的每一个学习问题都能在塾内得到专业、细致的解答，让同学带着满满的信心挑战留考。');?>
        </p>
      <?php else: ?>
        <p>
          <?=__('目前，新领域理工塾拥有逾百人规模的强大签约讲师团队。讲师均出身于国内、日本一流院校，覆盖专业广，教学质量高，在保证塾内课程内容完善的同时，又可为学生提供第一手大学院考学情报。进入梦想学校所需要的不仅是做题和考试，清晰可行的整体规划和细节执行同样与最终的成功密不可分。通过利用塾内独有的专业担当制度，哪怕是首次了解日本留学的学生也能全方位无死角地将最适合自己的留学计划制定好、理解透并且执行到位。无论是围绕专业内容的教学辅导，还是针对升学经验的总结分享，亦或是从零开始的留学规划，理工的漫漫考学路上，你需要什么，我们就能为你提供什么！');?>
        </p>
      <?php endif; ?>
    </div>
  </div>
  <div class="row mt-4">
    <?php foreach ($teachers as $teacher): ?>
      <div class="col-md-4">
        <div class="card card-border-style">
          <?php $teacher_description = str_replace(array("\r\n", "\r", "\n"), "<br>", ($current_lang == 'ch')?$teacher->description_ch:$teacher->description_jp); ?>
          <?php $teacher_name = ($current_lang == 'ch')?$teacher->name_ch:$teacher->name_jp; ?>
          <?php $teacher_image_url = ''; ?>
          <?php if (empty($teacher->url)): ?>
            <?php $teacher_image_url = 'Common/no_image.png'; ?>
          <?php else: ?>
            <?php $teacher_image_url = '../'.$teacher->url; ?>
          <?php endif; ?>
          <?=$this->Html->image($teacher_image_url, [
            'alt'=>__('No Image'),
            'class'=>'card-image',
            'onclick'=>'showTeacherDetailModal("'.$teacher_name.'", "'.$teacher->url.'", "'.$teacher_description.'")'
          ]);?>
          <div class="card-body news-text">
            <div><span class="badge badge-info"><?=h(($current_lang == 'ch')?$teacher->item->name_ch:$teacher->item->name_jp);?></span></div>
            <p class="card-text"><?=h(($current_lang == 'ch')?$teacher->name_ch:$teacher->name_jp);?></p>
          </div>
        </div>
      </div>
    <?php endforeach; ?>
  </div>
  <div class="paginator mt-5 horizon-center">
      <ul class="pagination">
          <?= $this->Paginator->first('<< ' . __('first')) ?>
          <?= $this->Paginator->prev('< ' . __('上一页')) ?>
          <?= $this->Paginator->numbers() ?>
          <?= $this->Paginator->next(__('下一页') . ' >') ?>
          <?= $this->Paginator->last(__('last') . ' >>') ?>
      </ul>
  </div>

  <div class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-labelledby="staticBackdropLabel" aria-hidden="true" id="teacherDetailModal">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="teacherDetailModalName"></h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-5">
              <?=$this->Html->image(null, ['alt'=>__('No Image'), 'style'=>'width: 100%;', 'id'=>'teacherDetailModalImage']);?>
            </div>
            <div class="col-md-7">
              <p id="teacherDetailModalDescription"></p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<style>
.card-body{
    overflow-y: scroll;
    overflow: -moz-scrollbars-none;/* 旧版本firefox对应 */
    -ms-overflow-style: none; /* IE 10+对应 */
    scrollbar-width: none; /* 最新版本firefox对应 */
}
.card-body::-webkit-scrollbar { 
    display: none; /* chrome和safari对应 */
    width: 0 !important
}
</style>