<?php
namespace App\Controller;

use App\Controller\AppController;
use Cake\I18n\Time;

/**
 * Teachers Controller
 *
 * @property \App\Model\Table\TeachersTable $Teachers
 *
 * @method \App\Model\Entity\Teacher[]|\Cake\Datasource\ResultSetInterface paginate($object = null, array $settings = [])
 */
class TeachersController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null
     */
    public function index()
    {
        $this->paginate = [
            'order'=>['rank'=>'DESC'],
            'contain' => ['Users', 'Items'],
        ];
        $teachers = $this->paginate($this->Teachers);

        if ($this->request->is('post')) {
          $conditions = array();
          $name_key = $this->request->getData('name_key');
          $conditions['item_id'] = $this->request->getData('category_key');
          if (!empty($name_key)) {
            $conditions['OR']['name_ch LIKE '] = '%'.$name_key.'%';
            $conditions['OR']['name_jp LIKE '] = '%'.$name_key.'%';
          }

          $teachers = $this->paginate($this->Teachers->find()->where($conditions));
        }
        $itemTable = $this->Teachers->Items;
        $items = $itemTable->find('list', [
          'keyField'=>'id',
          'valueField'=>function($itemTable){
            return $itemTable->get('label');
          }
        ])->where(['block'=>'3']);
        $this->set(compact('teachers', 'items'));
    }

    /**
     * View method
     *
     * @param string|null $id Teacher id.
     * @return \Cake\Http\Response|null
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $teacher = $this->Teachers->get($id, [
            'contain' => ['Users', 'Items'],
        ]);

        $this->set('teacher', $teacher);
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $teacher = $this->Teachers->newEntity();
        if ($this->request->is('post')) {
            $teacher = $this->Teachers->patchEntity($teacher, $this->request->getData());
            $teacher->user_id = $this->Auth->user('id');
            $teacher->created_at = Time::now();
            $teacher->updated_at = Time::now();
            if ($this->Teachers->save($teacher)) {
                $this->Flash->success(__('师资信息登录成功。'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('师资信息登录失败。请联系系统管理员并稍后尝试。'));
        }
        $itemTable = $this->Teachers->Items;
        $items = $itemTable->find('list', [
          'keyField'=>'id',
          'valueField'=>function($itemTable){
            return $itemTable->get('label');
          }
        ])->where(['block'=>'3']);
        $this->set(compact('teacher', 'items'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Teacher id.
     * @return \Cake\Http\Response|null Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $teacher = $this->Teachers->get($id, [
            'contain' => [],
        ]);
        if ($this->request->is(['patch', 'post', 'put'])) {
          $form_data = $this->request->getData();
            if (empty($form_data['url']['size'])){
              unset($form_data['url']);
            }
            $teacher = $this->Teachers->patchEntity($teacher, $form_data);
            $teacher->updated_at = Time::now();
            if ($this->Teachers->save($teacher)) {
                $this->Flash->success(__('师资信息更新成功。'));

                return $this->redirect(['action' => 'view', $id]);
            }
            $this->Flash->error(__('师资信息更新失败。请联系系统管理员并稍后尝试。'));
        }
        $itemTable = $this->Teachers->Items;
        $items = $itemTable->find('list', [
          'keyField'=>'id',
          'valueField'=>function($itemTable){
            return $itemTable->get('label');
          }
        ])->where(['block'=>'3']);
        $this->set(compact('teacher', 'items'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Teacher id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $teacher = $this->Teachers->get($id);
        if ($this->Teachers->delete($teacher)) {
            $this->Flash->success(__('师资信息删除成功。'));
        } else {
            $this->Flash->error(__('师资信息删除失败。请联系系统管理员并稍后尝试。'));
        }

        return $this->redirect(['action' => 'index']);
    }
}
